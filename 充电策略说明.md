# 电动冷藏车充电策略详解

本文档详细阐述电动冷藏车路径优化系统中的充电策略，包括充电决策机制、充电量计算方法及相关参数设定。

## 1. 充电策略概述

在电动冷藏车路径优化问题中，充电策略是指确定车辆何时、在何处以及充电量多少的决策过程。本系统采用基于需求的部分充电策略，即车辆不必充满电池，而是根据后续路径需求和预设的部分充电因子来确定充电量，从而提高配送效率。

## 2. 充电决策机制

### 2.1 充电时机判定

系统在以下情境下判定车辆需要充电：

1. **电量不足以到达下一客户点**：当车辆当前电量低于到达下一客户点所需能量时，系统将安排车辆前往充电站。所需能量包括行驶能耗和制冷能耗。

2. **电量不足以完成路径并返回配送中心**：当车辆完成客户服务后，若剩余电量不足以返回配送中心，系统将安排车辆前往充电站。

这可表述为：
$$\omega_i^v < e_{ij} \Rightarrow \text{需要充电}$$

其中：
- $\omega_i^v$：车辆$v$离开节点$i$时的电池电量（kWh）
- $e_{ij}$：从节点$i$到节点$j$的耗电量（kWh）

### 2.2 充电站选择

系统采用最近充电站选择策略，即选择距离当前位置欧氏距离最小的充电站：

$$s^* = \arg\min_{s \in S} d(i, s)$$

其中：
- $s^*$：选定的充电站
- $S$：充电站集合
- $d(i, s)$：节点$i$到充电站$s$的距离（km）

同时，系统会验证车辆是否有足够电量到达该充电站：

$$\omega_i^v \geq e_{is} \Rightarrow \text{可到达充电站}$$

若无法到达最近充电站，则当前路径被判定为不可行。

## 3. 充电量计算

### 3.1 充电量计算模型

实际系统中，充电量计算基于以下原则：

1. **目标充电水平**：电池容量的特定比例（由部分充电因子决定）
2. **最小充电需求**：确保至少满足后续路径所需能量
3. **充电上限**：不超过目标充电水平

充电量计算公式：

$$\phi_i^v = \min(E_{required}, B_v \cdot \alpha - \omega_i^v)$$

其中：
- $\phi_i^v$：车辆$v$在充电站$i$的充电量（kWh）
- $E_{required}$：后续路径所需能量（kWh）
- $B_v$：车辆$v$的电池容量（kWh）
- $\alpha$：部分充电因子（默认0.8）
- $\omega_i^v$：车辆$v$到达充电站$i$时的电池电量（kWh）

### 3.2 后续路径所需能量计算

后续路径所需能量包括：
1. 从充电站到下一客户点的能耗
2. 从客户点到配送中心的能耗
3. 行驶能耗与制冷能耗的综合

计算公式：

$$E_{required} = e_{si} + e_{i0}$$

其中：
- $e_{si}$：从充电站$s$到客户点$i$的耗电量（kWh）
- $e_{i0}$：从客户点$i$到配送中心（节点0）的耗电量（kWh）

耗电量计算包含行驶能耗和制冷能耗：

$$e_{ij} = d_{ij} \cdot r_{cons} + E_{refr}$$

其中：
- $d_{ij}$：节点$i$到节点$j$的距离（km）
- $r_{cons}$：单位距离耗电量（kWh/km）
- $E_{refr}$：制冷能耗（kWh）

### 3.3 充电时间计算

充电时间计算公式：

$$t_{charge} = \frac{\phi_i^v}{r_i}$$

其中：
- $t_{charge}$：充电时间（分钟）
- $\phi_i^v$：车辆$v$在充电站$i$的充电量（kWh）
- $r_i$：充电站$i$的充电速率（kWh/分钟）

## 4. 充电过程中的能耗

在充电过程中，制冷系统仍需运行以维持货物温度，这部分能耗从充电获得的电量中扣除：

$$\omega_i^v = \omega_i^v + \phi_i^v - E_{refr}$$

其中：
- $\omega_i^v$：车辆$v$离开充电站$i$时的电池电量（kWh）
- $\phi_i^v$：车辆$v$在充电站$i$的充电量（kWh）
- $E_{refr}$：充电期间制冷能耗（kWh）

制冷能耗计算：

$$E_{refr} = P_{refr} \cdot \frac{t}{60 \cdot \eta}$$

其中：
- $P_{refr}$：制冷功率（kW）
- $t$：时间（分钟）
- $\eta$：能效比（EER）

## 5. 部分充电策略的优势

采用部分充电策略而非完全充电策略具有以下优势：

1. **时间效率提升**：减少车辆在充电站的停留时间，加速配送流程
2. **配送效率优化**：更快完成配送任务，提高客户满意度
3. **充电站资源优化**：缩短充电时间，减少其他车辆等待时间
4. **电池寿命延长**：避免频繁完全充放电循环，延长电池使用寿命
5. **充电成本降低**：在电价高峰时段，仅充必要电量可降低成本

## 6. 参数设置

充电策略相关的主要参数包括：

| 参数 | 描述 | 默认值 | 单位 |
|------|------|--------|------|
| $\alpha$ | 部分充电因子 | 0.8 | - |
| $B_v$ | 车辆$v$的电池容量 | 40 | kWh |
| $r_{cons}$ | 单位距离耗电量 | 0.35 | kWh/km |
| $r_i$ | 充电站$i$的充电速率 | 0.5-1.5 | kWh/min |

## 7. 与理论模型的差异

系统实现的充电策略与理论模型存在一定差异：

### 7.1 理论模型

理论模型中，充电量计算公式为：

$$\phi = \max(\alpha \cdot (B_v - \omega_i^v), \beta \cdot B_v)$$

其中：
- $\phi$：充电量（kWh）
- $\alpha$：部分充电因子（0.6-0.8）
- $B_v$：车辆$v$的电池容量（kWh）
- $\omega_i^v$：车辆$v$到达充电站$i$时的电池电量（kWh）
- $\beta$：最小充电比例（0.2）

该公式确保充电量至少为电池容量的$\beta$比例（20%）。

### 7.2 实际实现模型

实际实现中，充电量计算公式为：

$$\phi_i^v = \min(E_{required}, B_v \cdot \alpha - \omega_i^v)$$

主要差异在于：
1. 理论模型使用$\max$函数确保最小充电量
2. 实际实现使用$\min$函数限制最大充电量
3. 实际实现未直接使用最小充电比例$\beta$，而是通过后续路径需求确保充电量满足需要

## 8. 充电成本计算

充电成本计算公式：

$$C_3 = \sum_{v \in V}\sum_{i \in S} CC_i \cdot \phi_i^v$$

其中：
- $C_3$：总充电成本（元）
- $V$：车辆集合
- $S$：充电站集合
- $CC_i$：充电站$i$的单位充电价格（元/kWh）
- $\phi_i^v$：车辆$v$在充电站$i$的充电量（kWh）
