# 电动汽车冷链物流配送路径优化系统 - 遗传算法的具体设计与实现

## 目录

1. [遗传算法概述](#1-遗传算法概述)
2. [染色体编码](#2-染色体编码)
3. [初始种群生成](#3-初始种群生成)
4. [选择操作](#4-选择操作)
5. [交叉操作](#5-交叉操作)
6. [变异操作](#6-变异操作)
7. [解码过程](#7-解码过程)
8. [适应度计算](#8-适应度计算)
9. [算法流程实现](#9-算法流程实现)
10. [高级特性](#10-高级特性)

## 1. 遗传算法概述

**总体概述**：遗传算法是一种基于自然选择和自然遗传学原理的启发式优化算法，在本系统中被用于解决具有多维约束的电动冷藏车路径规划问题。本算法模拟了生物进化过程，通过迭代优化来寻找接近全局最优的解决方案。

### 1.1 基本原理

遗传算法模拟了生物进化过程中的自然选择和遗传变异机制，通过种群迭代不断产生更优秀的个体，最终趋向于全局最优解。其核心机制包括：

- **自然选择**：适应度高的个体有更大概率被选择参与繁殖
- **遗传操作**：通过交叉和变异产生新的个体
- **种群更新**：新个体替代旧个体，种群不断进化

### 1.2 算法框架

```mermaid
flowchart TD
    A[初始化种群] --> B[评估适应度]
    B --> C[记录当前最佳解]
    C --> D{满足终止条件?}
    D -->|否| E[选择操作]
    E --> F[交叉操作]
    F --> G[变异操作]
    G --> H[评估新种群适应度]
    H --> I[更新最佳解]
    I --> D
    D -->|是| J[返回最佳解]
```

*遗传算法主要流程：初始化、适应度评估、选择、交叉、变异、更新种群*

### 1.3 优化目标

本系统中的遗传算法旨在最小化以下总成本：

$$
Z = \min(FC + DC + CC + RC + WC + PC + MC)
$$

其中：
- $FC$：固定成本
- $DC$：距离成本
- $CC$：充电成本
- $RC$：制冷成本
- $WC$：等待成本
- $PC$：时间惩罚成本
- $MC$：维护成本

## 2. 染色体编码

**总体概述**：本系统采用整数编码方案表示配送路径，通过特殊设计的染色体结构同时编码客户访问顺序和车辆分配信息。此编码方式能够高效表示复杂的配送路径，并便于进行遗传操作。**在本项目中，我们采用了整数编码加车辆分隔符的策略。**

### 2.1 编码方案

染色体由以下两部分组成：

1. **客户访问序列**：表示客户的访问顺序的整数序列
2. **车辆分隔符**：用于标记不同车辆路径的分隔点，计算规则为`n_customers + vehicle_id`

### 2.2 编码图示与示例

下图展示了一个包含6个客户、3辆车的染色体编码示例：

```mermaid
graph LR
    subgraph 染色体结构
    A[3] --- B[1] --- C[6] --- D[9] --- E[4] --- F[5] --- G[11] --- H[2]
    end
    
    subgraph 解码后路径
    I[配送中心] -->|车辆1| J[客户3] --> K[客户1] --> L[客户6] --> I
    I -->|车辆2| M[客户4] --> N[客户5] --> I
    I -->|车辆3| O[客户2] --> I
    end
```

*图1：染色体编码结构示例 - 前6个位置为客户序列，后2个为车辆分隔符*

染色体`[3, 1, 6, 9, 4, 5, 11, 2]`的解释：
- 客户序列部分：3, 1, 6, 4, 5, 2
- 分隔符部分：9, 11

根据代码中的实现，染色体分隔符的计算方式如下：
```
分隔符值 = 客户数量 + 当前车辆路径的最后一个客户在客户序列中的位置
```

在这个例子中，我们有6个客户，因此：
- 第一个分隔符值为9（= 6 + 3），表示第一辆车的路径到客户序列的第3个位置结束
- 第二个分隔符值为11（= 6 + 5），表示第二辆车的路径到客户序列的第5个位置结束
- 最后一辆车（第三辆）不需要分隔符，因为它自然到客户序列末尾结束

解码过程按照以下方式分割客户序列：
- 第一辆车：客户序列[0:3] = [3, 1, 6]
- 第二辆车：客户序列[3:5] = [4, 5]
- 第三辆车：客户序列[5:] = [2]

### 2.3 分隔符工作原理详解

根据代码中`encode`和`decode`方法的实现，分隔符的具体计算和使用过程如下：

1. **编码过程**：
   - 首先将所有客户节点按路径顺序添加到染色体
   - 然后计算每辆车路径的结束位置（累积位置）
   - 将这些位置加上客户总数作为分隔符值
   - 移除最后一辆车的分隔符（因为它自然到末尾结束）

2. **解码过程**：
   - 分离出客户序列和分隔符序列
   - 将分隔符值减去客户总数，再减1，得到分割点索引
   - 根据这些分割点将客户序列分成多个子路径
   - 为每个子路径添加配送中心作为起点和终点

这种编码方式与纯粹的"客户数量+车辆ID"不同，它使用了"客户数量+位置索引"的方式，这样的好处是：
- 可以直接定位到路径分割点，无需额外计算
- 避免了与充电站编号的混淆（充电站编号通常在路径构建阶段处理）
- 使解码过程更加高效

以下是一个更复杂的示例，展示6个客户、3辆车的另一种分配方式：

```
客户序列：[5, 2, 6, 1, 4, 3]
车辆分配：
- 车辆1：客户5, 客户2
- 车辆2：客户6, 客户1, 客户4
- 车辆3：客户3

计算分隔符：
- 第一辆车结束位置：2，分隔符 = 6 + 2 = 8
- 第二辆车结束位置：5，分隔符 = 6 + 5 = 11
- 最后一辆车不需要分隔符

最终染色体：[5, 2, 6, 1, 4, 3, 8, 11]
```

在解码过程中，系统计算：
- 分割点1 = 8 - 6 - 1 = 1 (索引从0开始，所以在位置1之后分割)
- 分割点2 = 11 - 6 - 1 = 4 (在位置4之后分割)

分割客户序列：
- 第一辆车：[5, 2]
- 第二辆车：[6, 1, 4]
- 第三辆车：[3]

通过这种方式，染色体既能简洁地表示完整的配送路径信息，又能高效地进行遗传操作。

### 2.4 编码优势

这种编码方式具有以下优点：
1. 能够灵活表示多车辆、多客户的复杂路径规划
2. 简化了染色体的遗传操作实现
3. 使解码过程直观明了
4. 便于处理车辆数量的动态变化

## 3. 初始种群生成

**总体概述**：初始种群的质量直接影响遗传算法的收敛速度和最终解的质量。本系统设计了多种初始解生成策略，通过综合运用这些策略，生成多样性和质量兼备的初始种群。**本项目真正采用了多策略混合的方式，按比例组合最近邻居、节约算法、时间导向、K-means聚类和随机策略。**

### 3.1 混合策略框架

系统使用多种策略混合的方式生成初始种群，根据不同策略的特点分配不同的比例：

```mermaid
pie
    title 初始种群生成策略比例分配
    "最近邻居策略" : 30
    "节约算法策略" : 20
    "时间导向策略" : 20
    "K-means聚类" : 20
    "随机生成策略" : 10
```

*图2：初始种群生成策略比例分配 - 各种策略按比例混合生成初始种群*

从源代码中可以看到，初始种群生成器在`generate_population`方法中确实采用了这五种策略的混合比例，计算每种策略需要生成的解决方案数量，并分别调用相应的生成方法。这种混合策略有助于提供多样性和高质量的初始解。

### 3.2 最近邻居策略

最近邻居策略是一种贪心算法，从一个随机客户开始，每次选择距离最近的下一个客户进行访问。

具体步骤：
1. 随机选择一个起始客户
2. 找出距离当前客户最近且满足约束条件的下一个客户
3. 将该客户添加到路径中
4. 重复步骤2-3直到不能再添加客户
5. 如果还有未分配的客户，选择下一辆车重复以上过程

```mermaid
graph TD
    A[随机选择起始客户]
    B[寻找最近的下一个客户]
    C[检查约束条件]
    D[添加客户到路径]
    E{所有客户都已分配?}
    F[完成当前路径]
    G[选择下一辆车]
    H{是否尝试过所有可能的客户?}
    I{还有可用车辆?}
    J[结束算法]
    
    A --> B
    B --> C
    C -->|满足| D
    C -->|不满足| H
    H -->|否| B
    H -->|是| F
    D --> E
    E -->|是| J
    E -->|否| F
    F --> I
    I -->|是| G
    I -->|否| J
    G --> A
```

*图3：最近邻居策略构建路径流程*

### 3.3 节约算法（Clarke-Wright）

Clarke-Wright节约算法计算合并两条路径可以节省的距离，按节约值降序排列，尝试合并路径。

具体步骤：
1. 初始状态：每个客户单独由一辆车服务
2. 计算所有可能的合并节约值
3. 按节约值从大到小排序
4. 依次尝试合并路径，同时检查约束条件
5. 直到无法再合并

```mermaid
graph TD
    A["初始状态: 每个客户单独一条路径"] --> B["计算所有可能合并的节约值"]
    B --> C["按节约值从大到小排序"]
    C --> D["选择最大节约值的客户对"]
    D --> E{"检查约束条件"}
    E -->|满足| F["合并路径"]
    E -->|不满足| D
    F --> G{"还有合并机会?"}
    G -->|是| D
    G -->|否| H["完成路径规划"]
```

*图4：节约算法工作流程 - 展示按节约值合并路径的过程*

### 3.4 时间导向策略

时间导向策略优先考虑时间窗口约束，按最早服务时间、最晚服务时间或时间窗口宽度排序，以构建满足时间窗口约束的路径。

具体步骤：
1. 根据时间窗口特性（最早时间/最晚时间/窗口宽度）对客户排序
2. 按照排序结果依次将客户分配到车辆
3. 每次分配时检查时间窗口和其他约束条件

```mermaid
flowchart TD
    A[按最早服务时间排序客户]
    B[选择下一个客户]
    C[为客户寻找最佳车辆]
    D{检查载重约束}
    E{检查时间窗口约束}
    F[分配客户到车辆]
    G{所有客户已分配?}
    H[完成路径规划]
    
    A --> B
    B --> C
    C --> D
    D -->|满足| E
    D -->|不满足| C
    E -->|满足| F
    E -->|不满足| C
    F --> G
    G -->|是| H
    G -->|否| B
```

*图5：时间导向策略流程图*

### 3.5 K-means聚类策略

K-means聚类策略根据客户的地理位置进行聚类，将相近的客户分配给同一辆车，适合解决大规模问题。

具体步骤：
1. 根据客户的地理坐标进行K-means聚类
2. 将聚类结果作为车辆分配方案
3. 对每个聚类内的客户应用TSP算法优化访问顺序

```mermaid
graph TD
    A[提取客户地理坐标]
    B[确定聚类数量k]
    C[执行K-means聚类]
    D[根据聚类结果分配客户到车辆]
    E[对每个簇内客户优化访问顺序]
    F[检查载重约束]
    G[调整分配结果]
    H[生成最终路径]
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F -->|违反约束| G
    F -->|满足约束| H
    G --> H
```

*图6：K-means聚类策略流程图*

### 3.6 随机生成策略

随机生成策略完全随机地生成解决方案，主要用于增加种群的多样性。

具体步骤：
1. 随机打乱所有客户的顺序
2. 按Round-Robin方式将客户分配给车辆
3. 生成染色体编码

```mermaid
graph TD
    A[随机打乱客户顺序]
    B[初始化车辆路径]
    C[顺序分配客户到车辆]
    D{检查载重约束}
    E[尝试其他车辆]
    F[生成染色体编码]
    G[解码和评估解决方案]
    
    A --> B
    B --> C
    C --> D
    D -->|满足| C
    D -->|不满足| E
    E --> C
    C -->|所有客户已分配| F
    F --> G
```

*图7：随机生成策略流程图*

### 3.7 初始种群生成策略比较

各种策略的特点比较如下：

| 策略 | 计算复杂度 | 解质量 | 多样性 | 适用场景 |
|------|------------|--------|--------|----------|
| 最近邻居 | 低 | 中 | 中 | 通用 |
| 节约算法 | 中 | 高 | 低 | 距离敏感 |
| 时间导向 | 低 | 中 | 中 | 时间窗口紧 |
| K-means聚类 | 高 | 高 | 中 | 地理集中 |
| 随机生成 | 极低 | 低 | 高 | 增加多样性 |

*表1：各种初始解生成策略的特点对比*

## 4. 选择操作

**总体概述**：选择操作是遗传算法中关键的一环，用于确定哪些个体能够参与繁殖并将基因传递给下一代。优质的选择机制应保持适当的选择压力，既能保留优秀个体，又能维持种群多样性。**从代码中可以看出，本项目虽然在config.py中配置了锦标赛选择方法，但实际实现支持多种选择策略（锦标赛、轮盘赌和排名选择），并与精英保留策略结合使用。**

### 4.1 锦标赛选择

锦标赛选择通过在种群中随机选取一定数量的个体（称为锦标赛规模），然后从中选择最优的个体，具有实现简单、计算效率高的特点。

具体步骤：
1. 从种群中随机选择k个个体（k为锦标赛规模，通常为2-5）
2. 选出这k个个体中适应度最好的个体
3. 将该个体作为父代参与后续的遗传操作

```mermaid
flowchart TD
    A[从种群随机选择k个个体] -->
    B[比较这k个个体的适应度] -->
    C[选择适应度最好的个体] -->
    D[返回选中的个体]
    
    subgraph 锦标赛示例 k=3
    E[个体1: 适应度120]
    F[个体4: 适应度95]
    G[个体5: 适应度110]
    end
    
    E --> B
    F --> B
    G --> B
    C --> H[选中个体4: 适应度95]
```

*图8：锦标赛选择流程图示例（k=3，选择适应度最小的个体）*

锦标赛选择的特点：
- 通过调整锦标赛规模k可以控制选择压力
- 无需对整个种群排序，计算效率高
- 适合并行实现，易于扩展到大型种群

### 4.2 轮盘赌选择

轮盘赌选择（也称为比例选择）根据个体的适应度比例分配选择概率，适应度越好的个体被选中的概率越高。

具体步骤：
1. 计算每个个体的适应度比例（对于最小化问题需要进行适应度转换）
2. 根据适应度比例为个体分配概率
3. 进行随机抽样选择个体

```mermaid
pie
    title 轮盘赌选择概率分布
    "个体2 (35%)" : 35
    "个体4 (25%)" : 25
    "个体5 (15%)" : 15
    "个体1 (10%)" : 10
    "个体3 (5%)" : 5
    "其他 (10%)" : 10
```

*图9：轮盘赌选择概率分布（扇区大小与个体的适应度成正比）*

轮盘赌选择的特点：
- 选择概率与适应度成正比，符合自然选择原理
- 在适应度差异很大时容易导致早熟收敛
- 在适应度差异很小时选择压力不足

### 4.3 排名选择

排名选择首先按适应度对个体进行排序，然后基于排名（而非直接的适应度值）分配选择概率，这有助于减缓选择压力，防止优秀个体过快占据种群。

具体步骤：
1. 按适应度值对种群进行排序
2. 根据排名（而非适应度值）分配选择概率
3. 根据概率选择个体

```mermaid
flowchart TD
    A[按适应度对种群排序] -->
    B[分配基于排名的选择概率] -->
    C[根据概率选择个体]
    
    subgraph 排名选择示例
    D["排名1: 个体2 (适应度85) 概率35%"]
    E["排名2: 个体4 (适应度95) 概率25%"]
    F["排名3: 个体5 (适应度110) 概率15%"]
    G["排名4: 个体1 (适应度120) 概率10%"]
    H["排名5: 个体3 (适应度150) 概率5%"]
    end
```

*图10：排名选择示例（为每个排名分配选择概率）*

排名选择的特点：
- 避免超级个体主导种群
- 防止适应度差异过大或过小导致的问题
- 计算复杂度较高，需要对整个种群排序

### 4.4 精英保留策略

精英保留策略直接将当前种群中适应度最好的几个个体（称为精英）保留到下一代，确保优秀个体不会在进化过程中丢失。

具体步骤：
1. 对当前种群按适应度排序
2. 保留前n个最好的个体（精英数量）
3. 其余位置通过正常的选择、交叉、变异产生

```mermaid
graph LR
    subgraph 当前种群
    A["个体2 (85)"] --> E
    B["个体4 (95)"] --> E
    C["个体5 (110)"] 
    D["个体1 (120)"]
    Z["个体3 (150)"]
    end
    
    subgraph 选择交叉变异
    C --> F
    D --> F
    Z --> F
    F["产生新个体"]
    end
    
    subgraph 新种群
    E["直接保留"]
    E --> G["个体2 (85)"]
    E --> H["个体4 (95)"]
    F --> I["新个体1"]
    F --> J["新个体2"]
    F --> K["新个体3"]
    end
```

*图11：精英保留策略示意图 - 前两名个体直接保留到下一代*

精英保留策略的特点：
- 确保最优解不会在进化过程中丢失
- 加速收敛速度
- 过多的精英可能导致多样性下降

### 4.5 选择策略的比较

不同选择策略的特点比较：

| 选择策略 | 选择压力 | 多样性保持 | 计算复杂度 | 应用场景 |
|---------|----------|------------|------------|----------|
| 锦标赛选择 | 可调 | 中 | 低 | 通用场景，特别是大种群 |
| 轮盘赌选择 | 中 | 中 | 中 | 适应度差异不太大的情况 |
| 排名选择 | 低 | 高 | 中 | 初期探索，防止早熟收敛 |
| 精英选择 | 高 | 低 | 低 | 与其他选择方法结合使用 |

*表2：不同选择策略的特点对比*

在本系统中，代码实现了多种选择策略，虽然默认配置使用锦标赛选择（tournament_size=3），但系统具备了使用其他选择方法的能力。其中，精英保留策略（elitism_selection）在每一代都会使用，将最佳个体（elite_size个，配置为2）直接保留到下一代。锦标赛选择的实现是从种群中随机选择tournament_size个个体，然后选出适应度最好的一个。

## 5. 交叉操作

**总体概述**：交叉操作模拟了生物繁殖中的基因重组过程，通过结合两个父代个体的特性创造后代。在车辆路径问题中，交叉操作需要特殊设计以保持解的有效性，确保不出现重复客户或遗漏客户的情况。**从代码实现来看，本项目支持多种交叉策略（顺序交叉、部分映射交叉、边重组交叉和自适应交叉），虽然默认配置使用顺序交叉(ordered)。**

### 5.1 顺序交叉(OX)

顺序交叉是一种保持相对顺序的交叉方法，适用于排列编码问题。它随机选取一个子序列，保持其顺序不变，剩余位置按照另一父代的顺序补全。

具体步骤：
1. 随机选择两个交叉点，确定中间段
2. 将第一个父代的中间段直接复制到子代相同位置
3. 从第二个交叉点开始，按照第二个父代中元素的顺序填充剩余位置，跳过已在中间段中的元素

```mermaid
flowchart TD
    subgraph 顺序交叉示例
    A["父代1: 3-5-1-4-2-8-7-6"] --> C
    B["父代2: 6-3-8-5-7-4-2-1"] --> D
    
    C["选择交叉区域：1-4-2"]
    D["选择交叉区域：8-5-7"]
    
    C --> E["子代1初始：_-_-1-4-2-_-_-_"]
    D --> F["子代2初始：_-_-8-5-7-_-_-_"]
    
    E --> G["填充子代1：6-8-1-4-2-5-7-3"]
    F --> H["填充子代2：3-1-8-5-7-4-2-6"]
    end
```

*图12：顺序交叉(OX)操作流程示例*

顺序交叉的特点：
- 保持部分元素的相对顺序
- 保持解的合法性（无重复元素）
- 适合路径规划类问题

### 5.2 部分映射交叉(PMX)

部分映射交叉通过建立两个父代之间的映射关系，生成满足排列约束的子代，适合处理排列编码问题。

具体步骤：
1. 随机选择两个交叉点，确定中间段
2. 将第一个父代的中间段直接复制到子代相同位置
3. 在两个父代的中间段之间建立映射关系
4. 填充剩余位置时，如果出现冲突，则根据映射关系替换

```mermaid
flowchart TD
    subgraph 部分映射交叉示例
    A["父代1: 3-5-1-4-2-8-7-6"] --> C
    B["父代2: 6-3-8-5-7-4-2-1"] --> D
    
    C["选择交叉区域：1-4-2"]
    D["选择交叉区域：8-5-7"]
    
    C --> E["建立映射关系：1↔8, 4↔5, 2↔7"]
    
    E --> F["子代1初始：_-_-1-4-2-_-_-_"]
    E --> G["子代2初始：_-_-8-5-7-_-_-_"]
    
    F --> H["填充子代1并解决冲突"]
    G --> I["填充子代2并解决冲突"]
    
    H --> J["子代1最终：6-3-1-4-2-7-8-5"]
    I --> K["子代2最终：3-1-8-5-7-4-2-6"]
    end
```

*图13：部分映射交叉(PMX)操作流程示例*

部分映射交叉的特点：
- 通过映射关系保持解的合法性
- 保持元素绝对位置而非相对顺序
- 更侧重于位置信息的继承

### 5.3 边重组交叉(ERX)

边重组交叉着重保持父代中相邻客户之间的连接关系，非常适合路径规划问题，因为路径规划更关注节点之间的连接关系而非节点的绝对位置。

具体步骤：
1. 为每个客户创建邻接表，记录在父代中与其相邻的所有客户
2. 随机选择一个起始客户
3. 每次从当前客户的邻接表中选择邻居数最少的客户作为下一个客户
4. 如果没有未访问的邻居，则随机选择一个未访问客户
5. 重复直到所有客户都被访问

```mermaid
flowchart TD
    subgraph 边重组交叉示例
    A["父代1路径: A→B→C→D→E→A"]
    B["父代2路径: A→C→E→B→D→A"]
    
    A --> C["构建邻接表"]
    B --> C
    
    C --> D["邻接表:
    A: {B,D,C,E}
    B: {A,C,E,D}
    C: {B,D,A,E}
    D: {C,E,A,B}
    E: {D,A,C,B}"]
    
    D --> E["选择起点：A"]
    E --> F["A的邻居都有4个连接，随机选择B"]
    F --> G["B的未访问邻居：C,D,E，选择C"]
    G --> H["C的未访问邻居：D,E，选择D"]
    H --> I["D的未访问邻居：E，选择E"]
    I --> J["路径完成：A→B→C→D→E→A"]
    end
```

*图14：边重组交叉(ERX)示例*

### 5.4 自适应交叉

自适应交叉根据父代的特性和当前种群状态，动态选择最合适的交叉方式，提高算法的适应性和鲁棒性。

具体策略：
1. 分析父代相似度、适应度差异和路径结构
2. 根据分析结果选择最合适的交叉算子：
   - 相似度高时使用边重组交叉保持共同结构
   - 适应度差异大时使用部分映射交叉
   - 路径数量差异大时使用顺序交叉

```mermaid
flowchart TD
    A[分析父代特性] --> B{相似度 > 0.7?}
    B -->|是| C[边重组交叉]
    B -->|否| D{适应度差异 > 0.3?}
    D -->|是| E[部分映射交叉]
    D -->|否| F{路径数量差异 > 2?}
    F -->|是| G[顺序交叉]
    F -->|否| G
    
    C --> H[生成子代]
    E --> H
    G --> H
```

*自适应交叉策略选择流程图*

自适应交叉的特点：
- 根据问题特性和演化阶段动态调整策略
- 结合多种交叉算子的优点
- 提高算法的鲁棒性和适应性

### 5.5 交叉策略的比较与选择

不同交叉策略的特点比较：

| 交叉策略 | 保持相对顺序 | 保持边连接 | 适用问题 | 计算复杂度 |
|---------|-------------|-----------|---------|------------|
| 顺序交叉(OX) | 高 | 中 | TSP, VRP | 中 |
| 部分映射交叉(PMX) | 中 | 低 | 排列问题 | 中 |
| 边重组交叉(ERX) | 低 | 高 | 路径问题 | 高 |
| 自适应交叉 | 视情况而定 | 视情况而定 | 复杂约束 | 高 |

*表3：交叉策略特点对比*

在本系统中，代码实现了四种交叉策略，虽然默认配置使用顺序交叉(ordered)，但系统具备使用其他交叉方法的能力。顺序交叉的实现细节包括：选择两个随机交叉点，保持父代中间段不变，按照另一父代的顺序填充剩余位置。交叉率设置为0.8，即80%的概率进行交叉操作。此外，源代码中的自适应交叉策略是一个高级功能，可以根据父代的特性动态选择最合适的交叉操作。

## 6. 变异操作

**总体概述**：变异操作是遗传算法中用于增加种群多样性、避免早熟收敛的关键机制。在路径规划问题中，变异操作通过对染色体进行小幅度改变，帮助算法跳出局部最优，探索更广阔的解空间。**从代码实现看，本项目支持多种变异策略（交换变异、插入变异、反转变异、扰动变异、路径分割、路径合并和自适应变异），虽然默认配置使用交换变异(swap)。**

### 6.1 交换变异

交换变异是最简单的变异操作，随机选择染色体中的两个客户位置并交换它们，能够在保持染色体结构的同时引入局部变化。

具体步骤：
1. 以变异率为概率决定是否进行变异
2. 随机选择染色体中的两个客户位置
3. 交换这两个位置的客户

```mermaid
flowchart TD
    subgraph 交换变异示例
    A["变异前: 3-5-1-4-2-8-7-6"] --> B["随机选择位置4和7"]
    B --> C["交换客户4和7"]
    C --> D["变异后: 3-5-1-7-2-8-4-6"]
    end
```

*图16：交换变异操作示例*

### 6.2 插入变异

插入变异将一个客户从当前位置移除并插入到另一个位置，可以更有效地改变客户的访问顺序，产生更大的路径变化。

具体步骤：
1. 以变异率为概率决定是否进行变异
2. 随机选择一个客户和一个插入位置
3. 将该客户从原位置移除，插入到新位置

```mermaid
flowchart TD
    subgraph 插入变异示例
    A["变异前: 3-5-1-4-2-8-7-6"] --> B["选择客户4和位置6"]
    B --> C["移除客户4"]
    C --> D["临时状态: 3-5-1-2-8-7-6"]
    D --> E["在位置6插入客户4"]
    E --> F["变异后: 3-5-1-2-8-4-7-6"]
    end
```

*图17：插入变异操作示例*

### 6.3 反转变异

反转变异通过反转染色体中的一个子序列，可以有效地改变局部路径，尤其适合解决TSP和VRP问题中的路径优化。

具体步骤：
1. 以变异率为概率决定是否进行变异
2. 随机选择染色体中的两个位置，确定子序列
3. 将该子序列完全反转

```mermaid
flowchart TD
    subgraph 反转变异示例
    A["变异前: 3-5-1-4-2-8-7-6"] --> B["选择子序列范围：1-4-2-8"]
    B --> C["反转子序列"]
    C --> D["变异后: 3-5-8-2-4-1-7-6"]
    end
```

*图18：反转变异操作示例*

### 6.4 扰动变异

扰动变异随机打乱染色体的一个子序列，与反转变异相比，产生的变化更加随机，能够带来更大的多样性。

具体步骤：
1. 以变异率为概率决定是否进行变异
2. 随机选择染色体中的两个位置，确定子序列
3. 随机打乱该子序列的顺序

```mermaid
flowchart TD
    subgraph 扰动变异示例
    A["变异前: 3-5-1-4-2-8-7-6"] --> B["选择子序列范围：1-4-2-8"]
    B --> C["随机打乱子序列"]
    C --> D["变异后: 3-5-8-4-1-2-7-6"]
    end
```

*图19：扰动变异操作示例*

### 6.5 路径分割/合并变异

针对VRP问题的特殊变异，通过分割或合并车辆路径改变资源分配，这类变异直接操作路径结构，对解的影响最为显著。

具体步骤（路径分割）：
1. 随机选择一条足够长的路径
2. 在路径中随机选择一个分割点
3. 将路径分为两条，分别分配给不同车辆

具体步骤（路径合并）：
1. 随机选择两条满足约束条件的相邻路径
2. 将两条路径合并为一条，分配给同一辆车

```mermaid
flowchart TD
    subgraph 路径分割示例
    A["原路径: 配送中心→1→2→3→4→5→配送中心"] --> B["选择分割点：客户3之后"]
    B --> C["分割为两条路径"]
    C --> D["路径1: 配送中心→1→2→3→配送中心"]
    C --> E["路径2: 配送中心→4→5→配送中心"]
    end
    
    subgraph 路径合并示例
    F["路径1: 配送中心→1→2→配送中心"] --> H["合并路径"]
    G["路径2: 配送中心→3→4→配送中心"] --> H
    H --> I["合并后: 配送中心→1→2→3→4→配送中心"]
    end
```

*图20：路径分割和合并变异示例*

### 6.6 自适应变异

自适应变异根据当前解的特性和算法运行状态，动态调整变异策略和变异率，提高算法效率。

具体策略：
1. 分析当前解的特征（路径数量、平均路径长度、成本构成等）
2. 根据特征选择合适的变异策略：
   - 使用车辆较少时，倾向于使用路径分割变异
   - 平均路径长度短时，倾向于使用路径合并变异
   - 距离成本占比高时，倾向于使用反转变异
   - 固定成本占比高时，倾向于使用路径合并变异

```mermaid
flowchart TD
    A[分析解的特性] --> B{使用车辆较少?}
    B -->|是| C[路径分割变异]
    B -->|否| D{平均路径长度短?}
    D -->|是| E[路径合并变异]
    D -->|否| F{距离成本占比高?}
    F -->|是| G[反转变异]
    F -->|否| H{固定成本占比高?}
    H -->|是| I[路径合并变异]
    H -->|否| J[均衡使用各种变异]
    
    C --> K[执行选定的变异操作]
    E --> K
    G --> K
    I --> K
    J --> K
```

*自适应变异策略选择流程图*

自适应变异的特点：
- 根据问题特性和求解阶段调整变异策略
- 提高算法的针对性和效率
- 平衡探索与开发能力

### 6.7 变异策略的比较与选择

不同变异策略的特点比较：

| 变异策略 | 局部/全局搜索 | 破坏性 | 适用问题 | 计算复杂度 |
|---------|--------------|-------|---------|------------|
| 交换变异 | 局部 | 低 | 通用 | 低 |
| 插入变异 | 中等 | 中 | 排序问题 | 低 |
| 反转变异 | 局部 | 中 | TSP, VRP | 低 |
| 扰动变异 | 局部 | 高 | 需求多样性 | 低 |
| 路径分割 | 全局 | 高 | VRP | 中 |
| 路径合并 | 全局 | 高 | VRP | 中 |
| 自适应变异 | 动态 | 动态 | 复杂约束 | 高 |

*表4：变异策略特点对比*

在本系统中，代码实现了七种变异策略，虽然默认配置使用交换变异(swap)，但系统具备使用其他变异方法的能力。交换变异的实现是随机选择两个客户位置并交换它们的值。变异率设置为0.2，即20%的概率进行变异操作。此外，源代码中的自适应变异策略是一个高级功能，可以根据解的特性和优化阶段动态选择最合适的变异操作。

## 7. 解码过程

**总体概述**：解码过程是将染色体转换为实际可行解决方案的关键步骤，在电动冷藏车路径问题中，解码需要考虑多维约束条件，包括载重约束、时间窗口约束、电池容量约束和温度控制约束等。本项目的解码过程包括路径构建和约束检查两个主要阶段。

### 7.1 解码基本流程

解码过程将染色体转换为车辆路径，并检查各种约束条件，基本流程如下：

```mermaid
flowchart TD
    A[输入染色体] --> B[分离客户和分隔符]
    B --> C[按分隔符划分路径]
    C --> D[为每条路径添加配送中心]
    D --> E[计算路径属性]
    E --> F[评估路径可行性]
    F --> G[计算总成本]
    G --> H[输出解决方案]
    
    E --> E1[计算行驶距离]
    E --> E2[计算时间窗口]
    E --> E3[计算电池消耗]
    E --> E4[计算温控需求]
    
    F --> F1[载重约束检查]
    F --> F2[时间窗约束检查]
    F --> F3[电池约束检查]
    F --> F4[温度约束检查]
```

*解码过程总体流程图*

解码的详细算法步骤如下：

**算法：解码过程**
```
输入：染色体chromosome、客户信息customers、车辆信息vehicles、距离矩阵distances
输出：解决方案solution

function decode(chromosome, customers, vehicles, distances):
    // 第一阶段：染色体到路径转换
    1. 分离客户序列和分隔符
       customer_sequence = chromosome中客户ID部分
       delimiter_sequence = chromosome中分隔符部分
    
    2. 计算分割点
       split_points = []
       for each 分隔符delimiter in delimiter_sequence:
           split_point = delimiter - len(customers) - 1
           split_points.append(split_point)
    
    3. 按分割点划分路径
       routes = []
       last_idx = 0
       for each 分割点point in split_points:
           route = customer_sequence[last_idx:point+1]
           routes.append(route)
           last_idx = point + 1
       
       // 添加最后一条路径
       if last_idx < len(customer_sequence):
           route = customer_sequence[last_idx:]
           routes.append(route)
    
    4. 为每条路径添加配送中心
       for each 路径route in routes:
           route.insert(0, 0)  // 开始于配送中心
           route.append(0)     // 返回配送中心
    
    // 第二阶段：路径评估和约束检查
    5. 初始化车辆状态
       for i = 0 to min(len(routes), len(vehicles)) - 1:
           vehicle = vehicles[i]
           route = routes[i]
           vehicle.initialize()
           vehicle.assign_route(route)
    
    6. 计算路径属性并检查约束
       for each 车辆vehicle in vehicles:
           if vehicle有分配路径:
               // 按顺序访问路径中的每个节点
               for j = 1 to len(vehicle.route) - 1:
                   current_node = vehicle.route[j-1]
                   next_node = vehicle.route[j]
                   
                   // 计算行驶距离和时间
                   distance = distances[current_node][next_node]
                   travel_time = distance / vehicle.speed
                   
                   // 更新车辆状态
                   if next_node > 0:  // 如果是客户节点
                       customer = customers[next_node-1]
                       vehicle.update_after_travel(next_node, distance, customer)
                       
                       // 检查各种约束
                       check_load_constraint(vehicle, customer)
                       check_time_window_constraint(vehicle, customer)
                       check_battery_constraint(vehicle, distance)
                       check_temperature_constraint(vehicle, customer)
                   else:  // 如果是配送中心
                       vehicle.update_after_travel(next_node, distance)
    
    7. 计算总成本
       total_cost = 计算固定成本 + 距离成本 + 时间成本 + 能源成本 + 温控成本
    
    8. 返回解决方案
       solution.routes = routes
       solution.cost = total_cost
       solution.is_feasible = 所有约束都满足
       return solution
```

### 7.2 染色体到路径的转换

解码的第一步是将染色体转换为具体的车辆路径。在实际代码中，染色体解码过程如下：

```
1. 染色体结构：[3, 1, 6, 9, 4, 5, 11, 2]  其中:
   - 前部分为客户序列: [3, 1, 6, 4, 5, 2]
   - 后部分为分隔符: [9, 11]

2. 分隔符计算规则:
   分隔符值 = 客户总数 + 分割点位置
   例如若有6个客户:
   - 分隔符9 = 6 + 3，表示在索引3处分割
   - 分隔符11 = 6 + 5，表示在索引5处分割

3. 分割客户序列:
   - 路径1: customer_sequence[0:3] = [3, 1, 6]
   - 路径2: customer_sequence[3:5] = [4, 5]
   - 路径3: customer_sequence[5:] = [2]

4. 添加配送中心:
   - 路径1: [0, 3, 1, 6, 0]
   - 路径2: [0, 4, 5, 0]
   - 路径3: [0, 2, 0]
```

真实代码中的完整解码过程伪代码如下：

```python
def decode(self):
    # 1. 分离客户和分隔符
    n_customers = len(self.customers)
    customer_sequence = [gene for gene in self.chromosome if gene <= n_customers]
    delimiter_sequence = [gene for gene in self.chromosome if gene > n_customers]
    
    # 2. 计算分割点
    split_points = [delimiter - n_customers - 1 for delimiter in delimiter_sequence]
    
    # 3. 划分路径
    routes = []
    start_idx = 0
    
    for point in split_points:
        routes.append(customer_sequence[start_idx:point+1])
        start_idx = point + 1
    
    # 添加最后一条路径
    if start_idx < len(customer_sequence):
        routes.append(customer_sequence[start_idx:])
    
    # 4. 为每条路径添加配送中心
    vehicle_routes = []
    for route in routes:
        vehicle_route = [0] + route + [0]  # 0表示配送中心
        vehicle_routes.append(vehicle_route)
    
    # 5. 分配路径给车辆
    for i, route in enumerate(vehicle_routes):
        if i < len(self.vehicles):
            self.vehicles[i].route = route
            self.vehicles[i].initialize()  # 重置车辆状态
    
    # 6. 评估解的可行性
    self.is_feasible = True
    
    # 7. 详细路径评估和约束检查
    # (在实际代码中，这部分会调用vehicle的方法进行详细计算)
    for vehicle in self.vehicles:
        if len(vehicle.route) > 2:  # 至少有一个客户
            # 按顺序访问路径中的每个节点
            for j in range(1, len(vehicle.route)):
                current_node = vehicle.route[j-1]
                next_node = vehicle.route[j]
                
                # 计算行驶距离和时间
                distance = self.distance_matrix[current_node][next_node]
                
                # 更新车辆状态和检查约束
                if next_node > 0:  # 客户节点
                    customer = self.customers[next_node-1]
                    is_feasible = vehicle.update_after_travel(next_node, distance, customer)
                    if not is_feasible:
                        self.is_feasible = False
                else:  # 配送中心
                    vehicle.update_after_travel(next_node, distance)
```

### 7.3 约束检查

在路径构建后，需要检查各种约束条件，确保解决方案的可行性：

1. **载重约束**：每辆车的载重不能超过其最大载重能力
   ```
   载重检查: Sum(客户需求量) ≤ 车辆最大载重
   ```

2. **时间窗口约束**：每个客户的服务必须在其时间窗口内进行
   ```
   时间窗口检查:
   客户最早开始时间 ≤ 实际到达时间 + 等待时间 ≤ 客户最晚开始时间
   ```

3. **电池容量约束**：车辆在行驶过程中电量不能为负
   ```
   电池容量检查:
   当前电量 - 行驶消耗电量 ≥ 0
   或者需要安排充电站
   ```

4. **温度控制约束**：保持货物在指定温度范围内
   ```
   温度控制检查:
   温度区间下限 ≤ 实际温度 ≤ 温度区间上限
   ```

### 7.4 多温区管理

电动冷藏车可能需要同时运输不同温度要求的商品，这就需要多温区管理：

```
+-----------------------------------------------------------+
|                   多温区管理示意图                          |
+-----------------------------------------------------------+
|                                                           |
|  车辆温区布局:                                              |
|  +---------------------------+                            |
|  |  冷冻区  |  冷藏区  |  常温区  |                          |
|  | -18°C  |   4°C   |  15°C   |                          |
|  +---------------------------+                            |
|                                                           |
|  客户货物温度要求:                                          |
|  客户1: -18°C (冷冻)                                       |
|  客户2: 4°C (冷藏)                                         |
|  客户3: 15°C (常温)                                        |
|  客户4: -18°C (冷冻)                                       |
|  客户5: 4°C (冷藏)                                         |
|  客户6: 4°C (冷藏)                                         |
|                                                           |
|  路径分配考虑:                                             |
|  路径1: [0, 1, 4, 0] (冷冻货物)                            |
|  路径2: [0, 2, 5, 6, 0] (冷藏货物)                         |
|  路径3: [0, 3, 0] (常温货物)                               |
|                                                           |
+-----------------------------------------------------------+
```

*多温区管理示意图：根据温度要求合理分配路径*

### 7.5 充电站规划

对于电动冷藏车，充电站的规划也是解码过程的重要部分：

```mermaid
flowchart LR
    A[配送中心] --> B[客户1]
    B --> C[客户2]
    C -->|电量不足| D[充电站]
    D --> E[客户3]
    E --> F[客户4]
    F --> A
```

*充电站规划示意图：在电量不足时安排充电站*

充电站规划策略：
1. **完全充电策略**：在充电站充满电池容量
2. **部分充电策略**：只充电到一定比例，节省充电时间
3. **智能充电策略**：根据剩余路径需求决定充电量

### 7.6 时间计算

时间计算是解码过程中的关键环节，需要考虑行驶时间、服务时间、等待时间和充电时间：

```
+-----------------------------------------------------------+
|                     时间计算示意图                          |
+-----------------------------------------------------------+
|                                                           |
|  客户时间窗口:                                              |
|  客户1: [8:00, 9:00]                                      |
|  客户2: [9:30, 10:30]                                     |
|  客户3: [11:00, 12:00]                                    |
|                                                           |
|  实际路径时间计算:                                          |
|  配送中心出发: 7:30                                         |
|  到达客户1: 7:50 (行驶20分钟)                               |
|  等待开始服务: 10分钟 (8:00才能开始)                          |
|  客户1服务: 8:00-8:20 (服务20分钟)                          |
|  行驶到客户2: 8:20-8:50 (行驶30分钟)                        |
|  客户2服务: 9:30-9:50 (等待40分钟后服务20分钟)                |
|  行驶到客户3: 9:50-10:30 (行驶40分钟)                       |
|  客户3服务: 11:00-11:20 (等待30分钟后服务20分钟)              |
|  返回配送中心: 11:20-12:00 (行驶40分钟)                     |
|                                                           |
|  总计:                                                    |
|  行驶时间: 130分钟                                          |
|  服务时间: 60分钟                                           |
|  等待时间: 80分钟                                           |
|  总用时: 270分钟                                           |
|                                                           |
+-----------------------------------------------------------+
```

*时间计算示意图：详细记录车辆在各节点的时间安排*

### 7.7 解码结果输出

解码完成后，系统输出以下信息作为解决方案：

1. 每辆车的具体路径
2. 每个客户的服务时间安排
3. 充电站的访问计划
4. 各项成本的详细计算
5. 解的可行性标记

```
+-----------------------------------------------------------+
|                    解码结果示意图                           |
+-----------------------------------------------------------+
|                                                           |
|  解决方案概览:                                              |
|  使用车辆数: 3                                              |
|  总行驶距离: 285.6公里                                      |
|  总行驶时间: 5小时30分钟                                     |
|  总服务时间: 3小时20分钟                                     |
|  总等待时间: 1小时15分钟                                     |
|  总充电时间: 40分钟                                         |
|  总成本: 2580.5元                                          |
|                                                           |
|  路径详情:                                                 |
|  车辆1: 配送中心 → 客户3 → 客户1 → 客户6 → 配送中心             |
|  车辆2: 配送中心 → 客户4 → 充电站1 → 客户5 → 配送中心           |
|  车辆3: 配送中心 → 客户2 → 配送中心                           |
|                                                           |
|  约束检查结果:                                              |
|  载重约束: 满足                                             |
|  时间窗口约束: 满足                                          |
|  电池容量约束: 满足                                          |
|  温度控制约束: 满足                                          |
|                                                           |
|  解的可行性: 可行                                           |
|                                                           |
+-----------------------------------------------------------+
```

*解码结果示意图：展示解码后的完整解决方案信息*

## 8. 适应度计算

**总体概述**：适应度计算是遗传算法中评估解决方案质量的核心机制，直接影响选择过程和算法的收敛方向。在电动冷藏车路径优化问题中，我们采用总成本最小化作为主要优化目标，通过详细的成本计算模型评估解决方案的优劣。**本项目采用加权多目标成本函数作为适应度评估标准。**

### 8.1 适应度函数定义

适应度函数通常定义为成本的倒数或负值，因为我们需要最小化成本但最大化适应度。在本项目中，适应度函数定义如下：

$$
Fitness(x) = \frac{1}{TotalCost(x) + PenaltyCost(x)}
$$

其中：
- $x$ 是一个解决方案（染色体）
- $TotalCost(x)$ 是解决方案的总成本
- $PenaltyCost(x)$ 是约束违反的惩罚成本

总成本函数进一步展开为：

$$
TotalCost(x) = \sum_{i=1}^{m} \left( FC_i + DC_i + CC_i + RC_i + WC_i + TC_i + MC_i \right)
$$

其中：
- $m$ 是使用的车辆数量
- $FC_i$ 是第i辆车的固定成本
- $DC_i$ 是第i辆车的距离成本
- $CC_i$ 是第i辆车的充电成本
- $RC_i$ 是第i辆车的制冷成本
- $WC_i$ 是第i辆车的等待成本
- $TC_i$ 是第i辆车的时间惩罚成本
- $MC_i$ 是第i辆车的维护成本

惩罚成本函数定义为：

$$
PenaltyCost(x) = \sum_{j=1}^{n} \left( w_j \times v_j(x) \right)
$$

其中：
- $n$ 是约束类型的数量
- $w_j$ 是第j类约束的惩罚权重
- $v_j(x)$ 是解决方案x对第j类约束的违反程度

各成本项的详细计算：

1. **固定成本 (FC)**：
   $$FC_i = C_{fixed} \times \delta_i$$
   - $C_{fixed}$: 每辆车的固定使用成本（元/辆）
   - $\delta_i$: 二元变量，若第i辆车被使用则为1，否则为0

2. **距离成本 (DC)**：
   $$DC_i = C_{distance} \times TotalDistance_i$$
   - $C_{distance}$: 单位距离成本（元/公里）
   - $TotalDistance_i$: 第i辆车的总行驶距离（公里）

3. **充电成本 (CC)**：
   $$CC_i = C_{charging} \times TotalChargingEnergy_i$$
   - $C_{charging}$: 单位充电成本（元/千瓦时）
   - $TotalChargingEnergy_i$: 第i辆车的总充电电量（千瓦时）

4. **制冷成本 (RC)**：
   $$RC_i = C_{refr\_idle} \times IdleTime_i + C_{refr\_load} \times LoadTime_i$$
   - $C_{refr\_idle}$: 空载制冷成本（元/小时）
   - $C_{refr\_load}$: 负载制冷成本（元/小时）
   - $IdleTime_i$: 第i辆车的空载时间（小时）
   - $LoadTime_i$: 第i辆车的负载时间（小时）

5. **等待成本 (WC)**：
   $$WC_i = C_{waiting} \times TotalWaitingTime_i$$
   - $C_{waiting}$: 等待成本（元/小时）
   - $TotalWaitingTime_i$: 第i辆车的总等待时间（小时）

6. **时间惩罚成本 (TC)**：
   $$TC_i = \sum_{j=1}^{k_i} \left( C_{early} \times EarlyTime_{ij} + C_{late} \times LateTime_{ij} \right)$$
   - $k_i$: 第i辆车服务的客户数量
   - $C_{early}$: 提前到达惩罚成本（元/小时）
   - $C_{late}$: 延迟到达惩罚成本（元/小时）
   - $EarlyTime_{ij}$: 第i辆车对第j个客户的提前到达时间（小时）
   - $LateTime_{ij}$: 第i辆车对第j个客户的延迟到达时间（小时）

7. **维护成本 (MC)**：
   $$MC_i = C_{maint\_idle} \times IdleTime_i + C_{maint\_load} \times LoadTime_i$$
   - $C_{maint\_idle}$: 空载维护成本（元/小时）
   - $C_{maint\_load}$: 负载维护成本（元/小时）

在实际代码中，适应度函数计算如下：

```python
def evaluate(self, cost_params):
    # 初始化各项成本
    self.fixed_cost = 0
    self.distance_cost = 0
    self.charging_cost = 0
    self.refrigeration_cost = 0
    self.waiting_cost = 0
    self.temp_violation_cost = 0
    self.refr_startup_cost = 0
    self.multi_temp_zone_cost = 0
    self.time_penalty_cost = 0
    self.maintenance_cost = 0
    self.damage_cost = 0
    
    # 计算各车辆的成本
    for vehicle in self.vehicles:
        if len(vehicle.route) > 2:  # 只考虑使用的车辆
            # 固定成本
            self.fixed_cost += cost_params['fixed_cost_per_vehicle']
            
            # 距离成本
            self.distance_cost += vehicle.total_distance * cost_params['distance_cost_per_km']
            
            # 充电成本
            self.charging_cost += vehicle.total_charging_energy * cost_params['charging_cost_per_kwh']
            
            # 制冷成本
            idle_hours = vehicle.total_idle_time / 60.0  # 转换为小时
            load_hours = vehicle.total_load_time / 60.0  # 转换为小时
            self.refrigeration_cost += (
                idle_hours * cost_params['refrigeration_cost_idle'] +
                load_hours * cost_params['refrigeration_cost_loading']
            )
            
            # 等待成本
            waiting_hours = vehicle.total_waiting_time / 60.0  # 转换为小时
            self.waiting_cost += waiting_hours * cost_params['waiting_cost_per_hour']
            
            # 维护成本
            self.maintenance_cost += (
                idle_hours * cost_params['maintenance_cost_idling'] +
                load_hours * cost_params['maintenance_cost_loading']
            )
            
            # 温度违规成本
            for violation in vehicle.temperature_violations:
                _, violation_degree, _ = violation
                self.temp_violation_cost += violation_degree * cost_params['temp_violation_penalty']
            
            # 其他成本...
    
    # 时间窗惩罚成本
    for vehicle in self.vehicles:
        for i, node_id in enumerate(vehicle.route):
            if node_id > 0 and node_id <= len(self.customers):
                customer = self.customers[node_id - 1]
                arrival_time = vehicle.schedule[i]['arrival_time']
                
                if arrival_time < customer.time_window[0]:  # 提前到达
                    early_hours = (customer.time_window[0] - arrival_time) / 60.0
                    self.time_penalty_cost += early_hours * cost_params['time_penalty_early']
                elif arrival_time > customer.time_window[1]:  # 延迟到达
                    late_hours = (arrival_time - customer.time_window[1]) / 60.0
                    self.time_penalty_cost += late_hours * cost_params['time_penalty_late']
    
    # 总成本计算
    self.fitness = (
        self.fixed_cost +
        self.distance_cost +
        self.charging_cost +
        self.refrigeration_cost +
        self.waiting_cost +
        self.temp_violation_cost +
        self.refr_startup_cost +
        self.multi_temp_zone_cost +
        self.time_penalty_cost +
        self.maintenance_cost +
        self.damage_cost
    )
    
    # 适应度是成本的倒数（需要最小化成本，但最大化适应度）
    # 注意：在实际代码中我们直接使用成本作为适应度，因为我们的选择操作是基于最小值
    return self.fitness
```

## 9. 算法流程实现

**总体概述**：遗传算法的实现需要将前面描述的所有组件整合起来，形成一个完整的优化流程。本系统的遗传算法实现包括初始化、选择、交叉、变异、评估和更新等环节，通过迭代优化不断提高解的质量。**本项目采用了标准遗传算法框架，结合问题特定的改进策略。**

### 9.1 算法主要流程

本项目遗传算法的实际实现流程如下图所示：

```mermaid
flowchart TD
    A[开始] --> B[初始化参数]
    B --> C[初始化种群]
    C --> D[计算适应度]
    D --> E[选择操作]
    E --> F[交叉操作]
    F --> G[变异操作]
    G --> H[精英保留]
    H --> I[生成新种群]
    I --> J{是否满足终止条件?}
    J -->|否| D
    J -->|是| K[输出最优解]
    K --> L[结束]
```

*图34：遗传算法主体流程图*

### 9.2 初始化模块

初始化模块负责设置算法参数和生成初始种群：

```mermaid
flowchart TD
    A[初始化模块] --> B[参数设置]
    A --> C[初始种群生成]
    
    B --> B1[设置种群规模]
    B --> B2[设置进化代数]
    B --> B3[设置交叉概率]
    B --> B4[设置变异概率]
    B --> B5[设置精英保留比例]
    
    C --> C1[获取客户数据]
    C --> C2[选择初始化策略]
    C2 --> C3[生成初始染色体]
    C3 --> C4[解码检查可行性]
    C4 --> C5[评估初始解质量]
```

*图35：初始化模块流程图*

### 9.3 进化模块

进化模块是遗传算法的核心，负责通过选择、交叉和变异操作不断改进解的质量：

```mermaid
flowchart TD
    A[进化模块] --> B[适应度评估]
    B --> C[选择操作]
    C --> D[交叉操作]
    D --> E[变异操作]
    E --> F[精英保留]
    F --> G[更新种群]
    G --> H{检查收敛性}
    H -->|未收敛| B
    H -->|收敛| I[终止进化]
```

*图36：进化模块流程图*

### 9.4 多线程并行计算

为了提高算法效率，我们采用多线程并行计算策略：

```mermaid
flowchart TD
    A["多线程并行模块"] --> B["主线程"]
    B --> C["划分计算任务"]
    C --> D["启动工作线程"]
    
    D --> E["工作线程1"]
    D --> F["工作线程2"]
    D --> G["工作线程n..."]
    
    E --> H["并行计算适应度"]
    F --> H
    G --> H
    
    H --> I["同步收集结果"]
    I --> J["合并评估结果"]
    J --> K["主线程继续"]
```

*图37：多线程并行计算流程图 - 展示并行计算提升算法效率的实现方式*

## 10. 高级特性

### 10.1 多目标优化

除成本最小化外，系统还考虑多个目标的平衡：

```mermaid
flowchart LR
    A[多目标优化] --> B[总成本目标]
    A --> C[车辆数量目标]
    A --> D[时间窗口满足度]
    A --> E[路径平衡性]
    A --> F[环保指标]
    
    B --> G[目标权重分配]
    C --> G
    D --> G
    E --> G
    F --> G
    
    G --> H[计算加权目标值]
    H --> I[Pareto非支配排序]
    I --> J[识别最优折衷解]
```

*图44：多目标优化流程图 - 展示多个优化目标的权衡决策过程*

### 10.2 学习与记忆机制

算法引入学习与记忆机制，利用历史经验指导搜索：

```mermaid
flowchart TD
    A[学习与记忆机制] --> B[维护历史解决方案库]
    B --> C[提取优秀解决方案模式]
    C --> D[分析高质量路径片段]
    
    D --> E[识别高频优质路径片段]
    E --> F[优先使用已验证优质片段]
    
    B --> G[问题特征提取与分类]
    G --> H[特征模式匹配]
    H --> I[检索类似历史问题]
    I --> J[参数知识迁移]
    
    F --> K[指导新解生成过程]
    J --> K
```

*图45：学习与记忆机制流程图 - 展示算法如何从历史经验中学习并应用到新问题*

### 10.3 实时调整与重规划

系统支持实时路径调整和重规划功能：

```mermaid
flowchart TD
    A[实时调整与重规划] --> B[持续监测执行情况]
    B --> C{是否产生执行偏差?}
    C -->|否| D[继续执行原计划]
    C -->|是| E[评估偏差的严重程度]
    
    E -->|轻微偏差| F[执行局部路径调整]
    E -->|严重偏差| G[触发全局路径重规划]
    
    F --> H[更新剩余执行路径]
    G --> I[重新执行规划算法]
    
    H --> J[继续执行调整后计划]
    I --> J
```

*图46：实时调整与重规划流程图 - 展示系统对执行过程中偏差的响应策略*

通过以上高级特性的实现和应用，遗传算法能够更好地应对电动冷藏车路径优化这类复杂问题，找到成本更低、更实用的物流配送方案。 
