实验: baseline
日期: 2025-03-30 09:12:28
总运行时间: 6.19秒

遗传算法参数:
  population_size: 20
  max_generations: 30
  elite_size: 2
  crossover_rate: 0.8
  mutation_rate: 0.2
  selection_method: tournament
  crossover_method: ordered
  mutation_method: swap

车辆参数:
  count: 8
  max_load: 2000
  battery_capacity: 85.22
  consumption_rate: 0.35
  speed: 45
  partial_charging_factor: 0.8
  refrigeration_power: 2.5
  refr_energy_ratio: 0.7
  temp_control_precision: 0.9
  multi_temp_zones: False
  idle_refr_power_ratio: 0.4

成本参数:
  fixed_cost_per_vehicle: 250
  distance_cost_per_km: 2.0
  charging_cost_per_kwh: 1.04
  refrigeration_cost_idle: 23.6
  refrigeration_cost_loading: 38.94
  waiting_cost_per_hour: 30
  time_penalty_early: 20
  time_penalty_late: 50
  maintenance_cost_idling: 12.33
  maintenance_cost_loading: 20.74
  damage_rate_loading: 0.8
  damage_rate_unloading: 0.6
  refrigeration_startup_cost: 5
  temp_violation_penalty: 50
  multi_temp_zone_cost: 8

优化结果:
  总成本: 3605.32
  固定成本: 500.00
  距离成本: 1040.15
  充电成本: 69.07
  制冷成本: 410.79
  等待成本: 0.00
  使用车辆数: 2
  总行驶距离: 520.08km

路径详情:
  车辆 1: 配送中心 客户7 客户3 客户22 客户29 客户19 客户16 客户8 客户2 客户25 客户21 客户12 客户24 客户6 客户13 客户11 客户15
  车辆 2: 配送中心 客户10 客户28 客户5 客户17 客户27 客户1 客户20 客户4 客户14 客户9 充电站1.0 客户30 客户26 客户23 客户18
