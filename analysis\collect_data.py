"""
数据收集模块，用于收集和分析实验数据
"""
import os
import sys
import time
import datetime
from datetime import datetime
import copy
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.config import GA_CONFIG, COST_PARAMS, VEHICLE_PARAMS, EXPERIMENT_CONFIG, FILE_PATHS
from src.models.solution import Solution
from src.models.customer import Customer
from src.models.vehicle import Vehicle
from src.models.charging import ChargingStation

class DataCollector:
    def __init__(self, solution, depot, customers, charging_stations, vehicles,
                 ga_config, vehicle_params, cost_params, experiment_runtime):
        """
        初始化数据收集器

        参数:
            solution (Solution): 解决方案对象
            depot (Customer): 配送中心对象
            customers (list): 客户对象列表
            charging_stations (list): 充电站对象列表
            vehicles (list): 车辆对象列表
            ga_config (dict): 遗传算法配置
            vehicle_params (dict): 车辆参数
            cost_params (dict): 成本参数
            experiment_runtime (float): 实验运行时间
        """
        self.solution = solution
        self.depot = depot
        self.customers = customers
        self.charging_stations = charging_stations
        self.vehicles = vehicles  # 实际车辆对象列表
        self.ga_config = ga_config
        self.vehicle_params = vehicle_params
        self.cost_params = cost_params
        self.experiment_runtime = experiment_runtime

        # 创建输出目录
        self.timestamp = datetime.now().strftime("%Y/%m/%d/%H:%M:%S")
        self.output_dir = os.path.join("analysis/outputs",
                                       self.timestamp.replace(':', '_'))
        os.makedirs(self.output_dir, exist_ok=True)

    def collect_and_save_data(self):
        """收集并保存所有数据"""
        report = self._generate_report()

        # 保存报告
        report_path = os.path.join(self.output_dir, "experiment_report.txt")
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(report)

        print(f"数据收集完成，报告已保存至: {report_path}")
        return report_path

    def _generate_report(self):
        """生成实验报告"""
        report = []

        # 1. 实验数据
        report.append("-------------------------")
        report.append("1.实验数据")
        report.append(f"日期：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"运行时间：{self.experiment_runtime:.2f}秒")
        report.append("-------------------------")

        # 2. 遗传算法参数
        report.append("2.遗传算法参数")
        report.append(f"种群数量：{self.ga_config['population_size']}")
        report.append(f"最大世代数：{self.ga_config['max_generations']}")
        report.append(f"精英个数: {self.ga_config['elite_size']}")
        report.append(f"交叉率：{self.ga_config['crossover_rate']}")
        report.append(f"突变率：{self.ga_config['mutation_rate']}")

        selection_methods = {
            'tournament': '锦标赛',
            'roulette': '轮盘赌',
            'rank': '排名'
        }
        crossover_methods = {
            'ordered': '有序',
            'pmx': 'PMX',
            'edge': '边重组',
            'adaptive': '自适应'
        }
        mutation_methods = {
            'swap': '交换',
            'insert': '插入',
            'inversion': '反转',
            'scramble': '扰乱',
            'route_split': '路径分割',
            'route_merge': '路径合并',
            'adaptive': '自适应'
        }

        report.append(f"选择方法: {selection_methods.get(self.ga_config['selection_method'], self.ga_config['selection_method'])}")
        report.append(f"交叉方法：{crossover_methods.get(self.ga_config['crossover_method'], self.ga_config['crossover_method'])}")
        report.append(f"突变方式：{mutation_methods.get(self.ga_config['mutation_method'], self.ga_config['mutation_method'])}")
        report.append("-------------------------")

        # 3. 车辆参数
        report.append("3.车辆参数")
        report.append(f"车辆数 ：{self.vehicle_params['count']}")
        report.append(f"最大负载 {self.vehicle_params['max_load']} kg")
        report.append(f"电池容量：{self.vehicle_params['battery_capacity']} kwh")
        report.append(f"单位距离耗电量：{self.vehicle_params['consumption_rate']} kWh/km")
        report.append(f"速度: {self.vehicle_params['speed']} km/h")
        report.append(f"部分充电系数: {self.vehicle_params['partial_charging_factor']}")
        report.append(f"制冷功率: {self.vehicle_params['refrigeration_power']}")
        report.append(f"制冷系统能效比: {self.vehicle_params['refr_energy_ratio']}")
        report.append(f"温度控制精度: {self.vehicle_params['temp_control_precision']}")
        report.append(f"是否支持多温区： {'是' if self.vehicle_params['multi_temp_zones'] else '否'}")
        report.append(f"停车时制冷功率比例(相对于行驶时):{self.vehicle_params['idle_refr_power_ratio']}")
        report.append("-------------------------")

        # 4. 成本参数
        used_vehicles = sum(1 for route in self.solution.vehicle_routes if len(route) > 2)
        total_distance = sum(vehicle.total_distance for vehicle in self.solution.vehicles if vehicle.total_distance > 0)

        # 从solution对象中获取各项成本
        fixed_cost = self.solution.fixed_cost
        distance_cost = self.solution.distance_cost
        charging_cost = self.solution.charging_cost
        refrigeration_cost = self.solution.refrigeration_cost
        temp_violation_cost = getattr(self.solution, 'temp_violation_cost', 0)
        refr_startup_cost = getattr(self.solution, 'refr_startup_cost', 0)
        multi_temp_zone_cost = getattr(self.solution, 'multi_temp_zone_cost', 0)
        time_penalty_cost = getattr(self.solution, 'time_penalty_cost', 0)
        early_arrival_penalty = getattr(self.solution, 'early_arrival_penalty', 0)
        late_arrival_penalty = getattr(self.solution, 'late_arrival_penalty', 0)
        maintenance_cost = getattr(self.solution, 'maintenance_cost', 0)
        damage_cost = getattr(self.solution, 'damage_cost', 0)

        # 计算空载和装载的制冷成本细分 - 这些值不会影响总成本，仅用于报告展示
        idle_refr_cost = 0
        loading_refr_cost = 0

        for vehicle in self.solution.vehicles:
            if vehicle.total_distance > 0:
                # 总运行时间(小时)
                total_operation_time = vehicle.total_travel_time / 60.0

                # 计算装载时间比例
                loading_ratio = sum(customer.demand for customer in self.customers if customer.id in vehicle.route) / vehicle.max_load
                loading_time_hours = total_operation_time * loading_ratio
                idle_time_hours = total_operation_time * (1 - loading_ratio)

                # 空载和装载的制冷成本
                idle_refr_cost += idle_time_hours * self.cost_params['refrigeration_cost_idle']
                loading_refr_cost += loading_time_hours * self.cost_params['refrigeration_cost_loading']

        # 确保空载和装载制冷成本之和等于总制冷成本
        # 如果有差异，按比例调整
        total_calculated_refr_cost = idle_refr_cost + loading_refr_cost
        if total_calculated_refr_cost > 0 and abs(total_calculated_refr_cost - refrigeration_cost) > 0.01:
            ratio = refrigeration_cost / total_calculated_refr_cost
            idle_refr_cost *= ratio
            loading_refr_cost *= ratio

        # 计算总等待时间、总提前时间和总延迟时间 - 完全参照src\debug_time_windows.py
        total_waiting_time = 0  # 分钟
        total_early_time = 0  # 分钟
        total_late_time = 0  # 分钟

        # 从车辆的调度计划中收集详细的时间信息 - 完全参照src\debug_time_windows.py
        for vehicle in self.solution.vehicles:
            if vehicle.total_distance <= 0:
                continue  # 跳过未使用的车辆

            # 累积总等待时间
            total_waiting_time += vehicle.total_waiting_time

            # 检查每个节点的时间窗口
            for i, node in enumerate(vehicle.schedule):
                node_id = node['node_id']
                arrival_time = node['arrival_time']
                waiting_time = node['waiting_time']

                # 如果是客户节点，检查时间窗口
                if node_id > 0 and node_id <= len(self.customers):
                    customer = self.customers[node_id - 1]
                    earliest_time = customer.time_window[0]
                    latest_time = customer.time_window[1]

                    # 检查提前到达
                    if arrival_time < earliest_time:
                        early_time = earliest_time - arrival_time
                        total_early_time += early_time

                    # 检查延迟到达
                    if arrival_time > latest_time:
                        late_time = arrival_time - latest_time
                        total_late_time += late_time

        # 计算维护成本细分 - 这些值不会影响总成本，仅用于报告展示
        idle_maintenance_cost = 0
        loading_maintenance_cost = 0

        for vehicle in self.solution.vehicles:
            if vehicle.total_distance > 0:
                # 使用与上面相同的时间计算
                total_operation_time = vehicle.total_travel_time / 60.0
                loading_ratio = sum(customer.demand for customer in self.customers if customer.id in vehicle.route) / vehicle.max_load
                loading_time_hours = total_operation_time * loading_ratio
                idle_time_hours = total_operation_time * (1 - loading_ratio)

                # 维护成本
                idle_maintenance_cost += idle_time_hours * self.cost_params['maintenance_cost_idling']
                loading_maintenance_cost += loading_time_hours * self.cost_params['maintenance_cost_loading']

        # 确保空载和装载维护成本之和等于总维护成本
        # 如果有差异，按比例调整
        total_calculated_maintenance_cost = idle_maintenance_cost + loading_maintenance_cost
        if total_calculated_maintenance_cost > 0 and abs(total_calculated_maintenance_cost - maintenance_cost) > 0.01:
            ratio = maintenance_cost / total_calculated_maintenance_cost
            idle_maintenance_cost *= ratio
            loading_maintenance_cost *= ratio

        # 时间窗成本总计 - 提前到达惩罚和延误到达惩罚
        time_window_cost = early_arrival_penalty + late_arrival_penalty

        # 总成本 - 直接从solution对象获取
        total_cost = self.solution.fitness

        # 验证总成本是否等于所有分项成本之和
        calculated_total = (fixed_cost + distance_cost + charging_cost +
                           refrigeration_cost + temp_violation_cost + refr_startup_cost + multi_temp_zone_cost +
                           early_arrival_penalty + late_arrival_penalty +
                           maintenance_cost + damage_cost)

        # 如果计算的总成本与solution.fitness不一致，使用solution.fitness作为准确值
        if abs(calculated_total - total_cost) > 0.1:
            print(f"警告: 计算的总成本 ({calculated_total:.2f}) 与solution.fitness ({total_cost:.2f}) 不一致")
            # 这里我们仍然使用solution.fitness作为准确值

        # 计算总充电量
        total_charge = 0
        for i, vehicle in enumerate(self.solution.vehicles):
            if vehicle.total_distance > 0 and hasattr(vehicle, 'total_charging_time'):
                if vehicle.total_charging_time > 0:
                    # 获取该车辆的路径
                    route = self.solution.vehicle_routes[i] if i < len(self.solution.vehicle_routes) else []

                    # 查找路径中的充电站
                    charging_stations_used = []
                    for node_id in route:
                        if node_id > len(self.customers):
                            station_idx = node_id - len(self.customers) - 1
                            if station_idx < len(self.charging_stations):
                                charging_stations_used.append(self.charging_stations[station_idx])

                    if charging_stations_used:
                        # 如果使用了多个充电站，按比例分配充电时间
                        # 这里简化处理，假设每个充电站充电时间相等
                        charging_time_per_station = vehicle.total_charging_time / len(charging_stations_used)

                        # 根据每个充电站的实际充电速率计算充电量
                        for station in charging_stations_used:
                            # 从总充电时间中减去排队时间，得到实际充电时间
                            actual_charging_time = charging_time_per_station - station.queue_time
                            if actual_charging_time > 0:
                                charging_rate = station.charging_rate  # 使用实际充电站的充电速率
                                total_charge += actual_charging_time * charging_rate
                    else:
                        # 如果找不到使用的充电站（异常情况），使用默认值
                        print(f"警告: 车辆{vehicle.id}有充电时间但在路径中找不到充电站，使用默认充电速率1.0 kWh/min")
                        total_charge += vehicle.total_charging_time * 1.0

        # 计算制冷相关成本总和
        total_refr_cost = refrigeration_cost + temp_violation_cost + refr_startup_cost + multi_temp_zone_cost

        # 计算时间窗成本总和 - 提前到达惩罚和延误到达惩罚
        time_window_cost = early_arrival_penalty + late_arrival_penalty

        # 计算维护相关成本总和
        maintenance_related_cost = maintenance_cost + damage_cost

        # 计算提前到达惩罚和延误到达惩罚 - 完全参照src\debug_time_windows.py
        # 使用总提前/延误时间(分钟)计算惩罚成本

        # 1. 提前到达惩罚
        time_penalty_early = self.cost_params['time_penalty_early']
        # 将分钟转换为小时计算惩罚成本
        early_hours = total_early_time / 60.0
        # 保留4位小数用于显示，然后四舍五入到2位小数
        report_early_time = round(early_hours, 2)  # 总提前到达时间(小时)
        # 计算提前到达惩罚 - 与debug_time_windows.py保持一致
        early_penalty = early_hours * time_penalty_early
        # 使用计算的惩罚值，而不是solution对象中的值
        report_early_penalty = round(early_penalty, 2)

        # 2. 延误到达惩罚
        time_penalty_late = self.cost_params['time_penalty_late']
        # 将分钟转换为小时计算惩罚成本
        late_hours = total_late_time / 60.0
        # 保留4位小数用于显示，然后四舍五入到2位小数
        report_late_time = round(late_hours, 2)  # 总延误到达时间(小时)
        # 计算延误到达惩罚 - 与debug_time_windows.py保持一致
        late_penalty = late_hours * time_penalty_late
        # 使用计算的惩罚值，而不是solution对象中的值
        report_late_penalty = round(late_penalty, 2)

        # 计算报告中的时间窗成本总和
        report_time_window_cost = report_early_penalty + report_late_penalty

        # 生成成本报告
        # 使用报告中的时间窗成本，而不是solution中的值
        # 确保包含所有成本项
        report_total_cost = (fixed_cost + distance_cost + charging_cost +
                            refrigeration_cost + temp_violation_cost + refr_startup_cost + multi_temp_zone_cost +
                            report_time_window_cost + maintenance_cost + damage_cost)

        # 确保所有成本项都使用相同的精度（2位小数）
        fixed_cost = round(fixed_cost, 2)
        distance_cost = round(distance_cost, 2)
        charging_cost = round(charging_cost, 2)
        refrigeration_cost = round(refrigeration_cost, 2)
        temp_violation_cost = round(temp_violation_cost, 2)
        refr_startup_cost = round(refr_startup_cost, 2)
        multi_temp_zone_cost = round(multi_temp_zone_cost, 2)
        report_time_window_cost = round(report_time_window_cost, 2)
        maintenance_cost = round(maintenance_cost, 2)
        damage_cost = round(damage_cost, 2)

        # 重新计算总成本，确保精度一致
        report_total_cost = (fixed_cost + distance_cost + charging_cost +
                            refrigeration_cost + temp_violation_cost + refr_startup_cost + multi_temp_zone_cost +
                            report_time_window_cost + maintenance_cost + damage_cost)

        # 确保总成本也使用相同的精度
        report_total_cost = round(report_total_cost, 2)

        # 如果计算的总成本与solution中的值相差太大，使用solution中的值
        if abs(report_total_cost - total_cost) > 1.0:
            print(f"警告: 计算的总成本 ({report_total_cost:.2f}) 与solution中的值 ({total_cost:.2f}) 相差较大")
            # 使用solution中的值，但在报告中保持各项成本的一致性
            total_cost = round(total_cost, 2)  # 确保solution中的值也使用相同的精度
            report.append("4.成本参数")
            report.append(f"总成本={total_cost:.2f}元=总固定成本({fixed_cost:.2f} 元)+总距离成本({distance_cost:.2f} 元)+总充电成本({charging_cost:.2f} 元)+总制冷成本({refrigeration_cost:.2f} 元)+总温度违规成本({temp_violation_cost:.2f} 元)+总时间窗成本({report_time_window_cost:.2f} 元)+总维护成本({maintenance_cost:.2f} 元)+总货物损坏成本({damage_cost:.2f} 元)")
        else:
            # 使用计算的总成本，确保等式完全成立
            report.append("4.成本参数")
            report.append(f"总成本={report_total_cost:.2f}元=总固定成本({fixed_cost:.2f} 元)+总距离成本({distance_cost:.2f} 元)+总充电成本({charging_cost:.2f} 元)+总制冷成本({refrigeration_cost:.2f} 元)+总温度违规成本({temp_violation_cost:.2f} 元)+总时间窗成本({report_time_window_cost:.2f} 元)+总维护成本({maintenance_cost:.2f} 元)+总货物损坏成本({damage_cost:.2f} 元)")
        report.append("")
        report.append("其中，各类成本的具体计算如下:")
        report.append(f"总固定成本({fixed_cost:.2f} 元)=使用车辆数（{used_vehicles} 辆）*每辆车的固定成本（{self.cost_params['fixed_cost_per_vehicle']} 元）")
        report.append("")
        report.append(f"总距离成本({distance_cost:.2f} 元)=总行驶距离（{total_distance:.2f} km）*每公里距离成本({self.cost_params['distance_cost_per_km']:.2f} 元/km)")
        report.append("")
        report.append(f"总充电成本({charging_cost:.2f} 元)=总充电量（{total_charge:.2f} kwh）*每千瓦时充电成本({self.cost_params['charging_cost_per_kwh']:.2f} 元/kwh)")
        report.append("")
        report.append(f"总制冷成本({refrigeration_cost:.2f} 元) = 闲置制冷成本({idle_refr_cost:.2f} 元) + 装载制冷成本({loading_refr_cost:.2f} 元)")
        report.append("")
        report.append(f"总温度违规成本({temp_violation_cost:.2f} 元)")
        report.append("")
        # 使用计算的时间窗成本，而不是solution中的值，确保等式成立
        report.append(f"总时间窗成本({report_time_window_cost:.2f} 元) = 提前到达惩罚({report_early_penalty:.2f} 元) + 延误到达惩罚({report_late_penalty:.2f} 元)")
        report.append("")
        report.append(f"其中，提前到达惩罚({report_early_penalty:.2f} 元) = 每小时提前到达惩罚({self.cost_params['time_penalty_early']:.2f} 元/h) * 总提前到达时间({total_early_time:.2f} 分钟 ({report_early_time:.4f} h))")

        # 显示延迟到达的惩罚（晚于客户最晚时间窗）- 完全参照src\debug_time_windows.py
        report.append(f"延误到达惩罚({report_late_penalty:.2f} 元) = 每小时延误到达惩罚({self.cost_params['time_penalty_late']:.2f} 元/h) * 总延误到达时间({total_late_time:.2f} 分钟 ({report_late_time:.4f} h))")

        report.append("")
        report.append(f"总维护成本({maintenance_cost:.2f} 元) = 空载维护成本({idle_maintenance_cost:.2f} 元) + 装载维护成本({loading_maintenance_cost:.2f} 元)")
        report.append("")
        report.append(f"总货物损坏成本({damage_cost:.2f} 元) = 装载损坏成本 + 卸载损坏成本")
        report.append("-------------------------")

        # 5. 配送路径
        active_routes = [route for route in self.solution.vehicle_routes if len(route) > 2]
        report.append("5.配送路径")
        report.append(f"总配送路线数量: {len(active_routes)}")

        # 遍历每条路线 - 完全参照visualization.py中的路径绘制代码
        for i, route in enumerate(active_routes):
            route_str = f"路线 {i+1}: 配送中心"

            # 遍历路径中的所有节点（包括起点和终点）
            for node_id in route:
                if node_id == 0:  # 配送中心
                    # 起点已经添加了，所以只为非起点的配送中心添加
                    if route_str != f"路线 {i+1}: 配送中心":
                        route_str += " -> 配送中心"
                elif 1 <= node_id <= len(self.customers):  # 客户
                    route_str += f" -> {node_id}"
                else:  # 充电站
                    station_idx = node_id - len(self.customers) - 1
                    route_str += f" -> 充电站{self.charging_stations[station_idx].id}"

            # 确保路径返回配送中心（如果最后一个节点不是配送中心）
            if route[-1] != 0:
                route_str += " -> 配送中心"

            report.append(route_str)

            # 添加路线长度和用时
            if i < len(self.solution.vehicles):
                vehicle = self.solution.vehicles[i]
                # 获取所有相关时间
                travel_time = getattr(vehicle, 'total_travel_time', 0)
                waiting_time = getattr(vehicle, 'total_waiting_time', 0)
                charging_time = getattr(vehicle, 'total_charging_time', 0)
                service_time = 0

                # 计算服务时间（如果schedule存在）
                if hasattr(vehicle, 'schedule'):
                    for node in vehicle.schedule:
                        service_time += node.get('service_time', 0)

                # 计算总时间（转换为小时）
                total_time = (travel_time + waiting_time + charging_time + service_time) / 60.0
                report.append(f"路线长度: {vehicle.total_distance:.2f} km, 用时: {total_time:.2f} 小时")
            report.append("")

        report.append("-------------------------")

        # 6. 各路线载重变化和客户需求量
        report.append("6.各路线载重变化和客户需求量")

        # 计算所有客户的总需求量
        total_customer_demand = sum(customer.demand for customer in self.customers)

        # 计算所有线路的初始载重总和
        all_routes_initial_load = total_customer_demand  # 假设所有客户都被服务，初始载重等于总需求量

        # 添加总载重和总需求量的比较
        report.append(f"各线路初始载重总和：{all_routes_initial_load:.1f}kg")
        report.append(f"总客户需求量：{total_customer_demand:.1f}kg")
        report.append("")

        # 假设所有客户都已被服务
        report.append("所有客户均已服务")
        report.append("")

        for i, route in enumerate(active_routes):
            if len(route) <= 2:  # 跳过空路线
                continue

            report.append(f"路线 {i+1} 载重变化:")

            # 初始载重
            if i < len(self.solution.vehicles):
                vehicle = self.solution.vehicles[i]
                max_load = self.vehicle_params['max_load']

                # 计算该路线的总需求量
                total_demand = 0
                # 遍历路径中的所有节点（包括起点和终点）
                for node_id in route:
                    if 1 <= node_id <= len(self.customers):
                        customer = self.customers[node_id - 1]
                        total_demand += customer.demand

                # 在配送中心的初始载重
                current_load = total_demand
                report.append(f"配送中心: {current_load:.1f} kg{'，在最大载重限制内' if current_load <= max_load else '，超出最大载重限制！'} ({max_load:.1f} kg)")

                # 沿路线更新载重
                # 遍历路径中的所有节点（包括起点和终点）
                for node_id in route:
                    if node_id == 0:  # 配送中心
                        # 跳过起点的配送中心，因为我们已经打印了初始载重
                        if node_id == route[0]:
                            continue
                        # 如果是终点的配送中心，会在循环结束后打印
                    elif 1 <= node_id <= len(self.customers):  # 客户
                        customer = self.customers[node_id - 1]
                        report.append(f"客户{node_id}: -{customer.demand:.1f} kg -> {current_load - customer.demand:.1f} kg")
                        current_load -= customer.demand

                report.append(f"配送中心: {current_load:.1f} kg")
            report.append("")

        report.append("-------------------------")

        # 7. 总体信息
        # 计算总行驶距离
        total_distance = sum(vehicle.total_distance for vehicle in self.solution.vehicles if vehicle.total_distance > 0)

        # 计算总用时 - 修复: 使用安全的属性访问
        total_time = 0
        for vehicle in self.solution.vehicles:
            if vehicle.total_distance > 0:
                travel_time = getattr(vehicle, 'total_travel_time', 0)
                waiting_time = getattr(vehicle, 'total_waiting_time', 0)
                charging_time = getattr(vehicle, 'total_charging_time', 0)
                total_time += (travel_time + waiting_time + charging_time) / 60.0  # 转换为小时

        # 计算总充电时间
        total_charging_time = sum(getattr(vehicle, 'total_charging_time', 0) for vehicle in self.solution.vehicles) / 60.0  # 转换为小时

        # 计算总制冷时间（可以从车辆中提取，或者根据总时间粗略估算）
        total_refrigeration_time = sum(getattr(vehicle, 'total_refrigeration_time', 0)
                                      for vehicle in self.solution.vehicles) / 60.0  # 转换为小时

        # 如果没有专门的制冷时间属性，使用行驶时间作为近似
        if total_refrigeration_time == 0:
            total_refrigeration_time = sum(getattr(vehicle, 'total_travel_time', 0)
                                          for vehicle in self.solution.vehicles if vehicle.total_distance > 0) / 60.0

        # 计算电量消耗
        total_energy_consumption = sum(getattr(vehicle, 'consumption_rate', 0) * vehicle.total_distance
                                      for vehicle in self.solution.vehicles if vehicle.total_distance > 0)

        # 计算制冷电量消耗 - 使用制冷功率和时间
        total_refrigeration_energy = 0
        for vehicle in self.solution.vehicles:
            if vehicle.total_distance > 0:
                # 总运行时间(小时)
                total_operation_time = vehicle.total_travel_time / 60.0

                # 制冷功率和能效比
                refr_power = getattr(vehicle, 'refrigeration_power', 2.5)
                refr_energy_ratio = getattr(vehicle, 'refr_energy_ratio', 0.7)

                # 计算装载和空载时间及其制冷能耗
                loading_ratio = sum(customer.demand for customer in self.customers if customer.id in vehicle.route) / vehicle.max_load
                loading_time_hours = total_operation_time * loading_ratio
                idle_time_hours = total_operation_time * (1 - loading_ratio)

                # 计算制冷能耗
                idle_power = refr_power * getattr(vehicle, 'idle_refr_power_ratio', 0.4)
                idle_energy = idle_time_hours * idle_power / refr_energy_ratio
                loading_energy = loading_time_hours * refr_power / refr_energy_ratio

                total_refrigeration_energy += (idle_energy + loading_energy)

        report.append("7.总体信息")
        report.append(f"总行驶距离: {total_distance:.2f} km")
        report.append("")
        report.append(f"总用时时长: {total_time:.2f} h")

        # 计算总充电时间的详细信息
        total_charging_minutes = total_charging_time * 60  # 转换为分钟

        # 计算总排队时间
        total_queue_time = 0
        for i, vehicle in enumerate(self.solution.vehicles):
            if vehicle.total_distance > 0 and hasattr(vehicle, 'total_charging_time') and vehicle.total_charging_time > 0:
                # 获取该车辆的路径
                route = self.solution.vehicle_routes[i] if i < len(self.solution.vehicle_routes) else []

                # 查找路径中的充电站
                charging_stations_used = []
                for node_id in route:
                    if node_id > len(self.customers):
                        station_idx = node_id - len(self.customers) - 1
                        if station_idx < len(self.charging_stations):
                            charging_stations_used.append(self.charging_stations[station_idx])

                # 计算排队时间
                if charging_stations_used:
                    for station in charging_stations_used:
                        total_queue_time += station.queue_time

        # 计算实际充电时间（不含排队时间）
        actual_charging_minutes = total_charging_minutes - total_queue_time

        # 添加充电时间详细信息
        report.append(f"总充电时长: {total_charging_time:.2f} h ({total_charging_minutes:.2f} 分钟)")
        report.append(f"  = 实际充电时间: {actual_charging_minutes/60:.2f} h ({actual_charging_minutes:.2f} 分钟)")
        report.append(f"  + 充电站排队时间: {total_queue_time/60:.2f} h ({total_queue_time:.2f} 分钟)")

        report.append(f"总制冷时间：{total_refrigeration_time:.2f} h")
        # 将分钟转换为小时显示 - 完全参照src\debug_time_windows.py
        report.append(f"总提前到达时长: {total_early_time:.2f} 分钟 ({total_early_time/60:.4f} h)")
        report.append(f"总延误到达时长: {total_late_time:.2f} 分钟 ({total_late_time/60:.4f} h)")
        report.append("")
        report.append(f"总电量消耗: {total_energy_consumption:.2f} kwh")
        report.append(f"总充电电量: {total_charge:.2f} kwh")
        report.append(f"总制冷电量消耗：{total_refrigeration_energy:.2f} kwh")

        return "\n".join(report)


def collect_data(solution, depot, customers, charging_stations, vehicles,
                ga_config, vehicle_params, cost_params, experiment_runtime):
    """
    收集并保存数据

    参数:
        solution (Solution): 解决方案对象
        depot (Customer): 配送中心对象
        customers (list): 客户对象列表
        charging_stations (list): 充电站对象列表
        vehicles (list): 车辆对象列表
        ga_config (dict): 遗传算法配置
        vehicle_params (dict): 车辆参数
        cost_params (dict): 成本参数
        experiment_runtime (float): 实验运行时间

    返回:
        str: 报告保存路径
    """
    collector = DataCollector(
        solution, depot, customers, charging_stations, vehicles,
        ga_config, vehicle_params, cost_params, experiment_runtime
    )

    return collector.collect_and_save_data()


if __name__ == "__main__":
    print("此模块不适合直接运行，请从main.py调用")