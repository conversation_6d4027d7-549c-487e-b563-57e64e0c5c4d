# 电动冷藏车物流配送路径优化系统成本模型详解

## (1) 固定运输成本

每辆纯电动冷藏车被分派出去执行城市冷链配送任务时，都需要支付固定的运输成本，包括车辆和车载冷藏压缩机组的使用成本与折旧成本、驾驶员的工资等。在一个确定的地点和时间段里，固定运输成本确定的，不受其余因素影响。

对于每辆电动冷藏车$v \in V$，其固定成本$F_v$（单位：元/辆）表示为：

$$F_v = \sum_{v \in V} c_1 \cdot y_v$$

其中，$c_1$为单位车辆固定成本（元/辆），$y_v$为表示车辆$v$是否被使用的二元变量，若使用则$y_v=1$，否则$y_v=0$。

则在进行城市冷链配送时所产生的总固定运输成本$C_1$为：

$$C_1 = \sum_{v \in V} F_v$$

## (2) 可变运输成本

纯电动冷藏车的可变运输成本主要包括车辆行驶过程中的动力系统电量消耗。一般车辆的可变运输成本与城市配送距离成正比，则纯电动冷藏车在配送时产生的总可变运输成本$C_2$为：

$$C_2 = \sum_{k \in K}\sum_{i \in I}\sum_{j \in J} c_{ij}^k x_{ij}^k$$

其中：
- $x_{ij}^k \in \{0,1\}$：若车辆$k$从节点$i$直接行驶至节点$j$，则$x_{ij}^k=1$，否则为0
- $c_{ij}^k$：车辆$k$从节点$i$到节点$j$的单位运输成本（元/km），主要考虑动力系统的能耗
- $K$：车辆集合
- $I,J$：节点集合（包括客户点和充电站）

## (3) 充电成本

充电成本主要由电动冷藏车在充电站进行充电时所消耗的电费构成，总充电成本$C_3$为：

$$C_3 = \sum_{v \in V}\sum_{i \in S} CC_i \cdot \phi_i^v$$

其中：
- $\phi_i^v$：车辆$v$在充电站$i$的充电量（单位：kWh）
- $CC_i$：充电站$i$的单位充电价格（单位：元/kWh）
- $S$：充电站集合

### 3.1 充电量计算

充电量$\phi_i^v$的计算基于部分充电策略，该策略允许车辆在充电站只充到电池容量的一定比例（而非完全充满），以节省充电时间。充电量计算公式为：

$$\phi_i^v = \max(\alpha \cdot (B_v - \omega_i^v), \beta \cdot B_v)$$

其中：
- $\phi_i^v$：车辆$v$在充电站$i$的充电量（kWh）
- $\alpha$：部分充电因子（通常为0.6-0.8）
- $B_v$：车辆$v$的电池容量（kWh）
- $\omega_i^v$：车辆$v$到达充电站$i$时的电池电量（kWh）
- $\beta$：最小充电比例（通常为0.2）

实际充电时间计算：

$$t_{charge} = \frac{\phi_i^v}{r_i}$$

其中：
- $t_{charge}$：充电时间（分钟）
- $r_i$：充电站$i$的充电速率（kWh/min）

电池电量连续性约束：

$$\omega_i^v - e_{ij} \cdot x_{ij}^v \geq \omega_j^v - M(1-x_{ij}^v), \forall i \in N, \forall j \in C \cup \{0\}, \forall v \in V$$

其中$e_{ij}$是从节点$i$到节点$j$的耗电量，包括行驶能耗和制冷能耗。

## (4) 制冷成本

制冷成本包括车辆在空载状态和装载状态下的不同能耗成本，根据装载状态将制冷成本$C_4$分为两部分：

$$C_4 = \sum_{v \in V} (RC_v^{loaded} + RC_v^{idle})$$

其中：
- $RC_v^{loaded}$：车辆$v$装载货物时的制冷成本（元）
- $RC_v^{idle}$：车辆$v$空载时的制冷成本（元）

对于装载状态的制冷成本，计算公式为：

$$RC_v^{loaded} = P_{base} \cdot \frac{t_{loaded}}{60 \cdot \eta} \cdot p_{e}$$

对于空载状态的制冷成本，计算公式为：

$$RC_v^{idle} = P_{base} \cdot \gamma_{idle} \cdot \frac{t_{idle}}{60 \cdot \eta} \cdot p_{e}$$

其中：
- $P_{base}$：基础制冷功率（单位：kW）
- $t_{loaded}$：装载状态下运行时间（单位：分钟）
- $t_{idle}$：空载状态下运行时间（单位：分钟）
- $\eta$：能效比(EER)（无量纲），表示制冷系统的能源效率，是制冷量与消耗电能的比值
- $\gamma_{idle}$：空载功率比例因子（无量纲，范围0.3-0.5），表示空载状态下制冷功率相对于满载状态的比例
- $p_{e}$：制冷系统单位时间运行成本（单位：元/kWh），这是一个综合参数，包含了电价和制冷系统运行的其他成本因素

装载时间比例的计算基于车辆载重与最大载重的比例：

$$t_{loaded} = t_{total} \cdot \frac{\sum_{i \in C_v} d_i}{Q_v}$$

$$t_{idle} = t_{total} \cdot (1 - \frac{\sum_{i \in C_v} d_i}{Q_v})$$

其中：
- $t_{total}$：车辆总运行时间（单位：分钟）
- $C_v$：车辆$v$服务的客户集合
- $d_i$：客户$i$的需求量（单位：kg）
- $Q_v$：车辆$v$的最大载重（单位：kg）

## (5) 时间相关成本

时间相关成本包括两个独立的部分：提前到达产生的等待成本和延迟到达产生的惩罚成本。每个客户都有一个服务时间窗口[$a_i$, $b_i$]，在此区间内到达不产生额外成本。

### 5.1 等待成本（提前到达）

当车辆早于客户最早服务时间($a_i$)到达时，需要等待至允许服务时间，这段等待时间产生等待成本$WC_v$：

$$WC_v = \sum_{i \in C} \max(0, a_i - \tau_i^v) \cdot \frac{c_w}{60}$$

其中：
- $a_i$：客户$i$的最早服务时间（分钟）
- $\tau_i^v$：车辆$v$到达客户$i$的时间（分钟）
- $c_w$：单位等待成本（元/小时）
- $C$：客户集合

### 5.2 时间惩罚成本（延迟到达）

当车辆晚于客户最晚服务时间($b_i$)到达时，产生延迟惩罚成本$PC_v$：

$$PC_v = \sum_{i \in C} \max(0, \tau_i^v - b_i) \cdot \frac{c_p}{60}$$

其中：
- $b_i$：客户$i$的最晚服务时间（分钟）
- $c_p$：单位延迟惩罚成本（元/小时），通常$c_p > c_w$以体现延迟的严重性

### 5.3 时间窗口成本函数

时间窗口成本是一个分段函数：
1. 提前到达段（t < $a_i$）：线性增加的等待成本
2. 时间窗口段（$a_i$ ≤ t ≤ $b_i$）：零成本
3. 延迟到达段（t > $b_i$）：线性增加的惩罚成本，斜率大于等待成本

## (6) 维护成本

维护成本包括车辆的定期检查、保养维修等费用，根据车辆运行状态分为空载维护成本和装载维护成本：

$$MC_v = MC_v^{idle} + MC_v^{loaded}$$

其中：
- $MC_v^{idle}$：车辆$v$空载时的维护成本（元），包括基础检查和保养费用
- $MC_v^{loaded}$：车辆$v$装载时的维护成本（元），包括额外的载重相关维护费用

对于空载状态的维护成本，计算公式为：

$$MC_v^{idle} = \frac{t_{idle}}{60} \cdot c_{maint\_idle}$$

对于装载状态的维护成本，计算公式为：

$$MC_v^{loaded} = \frac{t_{loaded}}{60} \cdot c_{maint\_loaded}$$

其中：
- $t_{idle}$：车辆$v$空载状态下的运行时间（分钟）
- $t_{loaded}$：车辆$v$装载状态下的运行时间（分钟）
- $c_{maint\_idle}$：空载维护单位时间成本（元/小时），默认值为12.33元/小时
- $c_{maint\_loaded}$：装载维护单位时间成本（元/小时），默认值为20.74元/小时

装载时间和空载时间的计算基于车辆载重与最大载重的比例：

$$t_{loaded} = t_{total} \cdot \frac{\sum_{i \in C_v} d_i}{Q_v}$$

$$t_{idle} = t_{total} \cdot (1 - \frac{\sum_{i \in C_v} d_i}{Q_v})$$

其中：
- $t_{total}$：车辆$v$的总运行时间（分钟）
- $C_v$：车辆$v$服务的客户集合
- $d_i$：客户$i$的需求量（kg）
- $Q_v$：车辆$v$的最大载重（kg）

维护成本反映了车辆在不同负载状态下的磨损和维护需求差异，装载状态下的维护成本通常高于空载状态，这是因为载重会增加车辆部件的磨损和故障风险。

## (7) 温度违规成本

当货物温度超出客户要求范围时，产生温度违规惩罚成本$TC_v$：

$$TC_v = \sum_{i \in C} (\max(0, \theta_i^v - \theta_{max}^i) + \max(0, \theta_{min}^i - \theta_i^v)) \cdot c_t$$

其中：
- $\theta_i^v$：车辆$v$到达客户$i$时的货物温度（°C）
- $\theta_{max}^i$：客户$i$要求的最高温度（°C）
- $\theta_{min}^i$：客户$i$要求的最低温度（°C）
- $c_t$：单位温度违规惩罚成本（元/°C）

## (8) 总成本目标函数

系统的总成本优化目标为：

$$Z = \min\left( C_1 + C_2 + C_3 + C_4 + \sum_{v \in V} WC_v + \sum_{v \in V} PC_v + \sum_{v \in V} MC_v + \sum_{v \in V} TC_v \right)$$

上述目标函数综合考虑了电动冷藏车配送过程中的固定成本、可变运输成本、充电成本、制冷成本、时间相关成本、维护成本和温度违规成本，通过遗传算法求解最优配送路径，以实现总成本最小化。

## 各成本单位说明

1. 固定运输成本：元/辆
2. 可变运输成本：元/km
3. 充电成本：元/kWh
4. 制冷成本：元/h
5. 等待成本：元/h
6. 时间惩罚成本：元/h
7. 维护成本：元/h
8. 温度违规成本：元/°C

## 成本关系图

固定成本和可变成本是基础成本，充电成本和制冷成本是电动冷藏车特有的成本项目，时间相关成本和温度违规成本则是与服务质量相关的惩罚成本。这些成本共同构成了电动冷藏车冷链物流配送的总成本，通过优化算法，可以在保证服务质量的前提下，最小化总配送成本。