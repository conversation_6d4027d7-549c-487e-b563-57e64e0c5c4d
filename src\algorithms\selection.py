"""
选择操作模块，实现多种选择策略
"""
import numpy as np
import random


class SelectionOperator:
    """选择操作类，支持多种选择策略"""
    
    @staticmethod
    def tournament_selection(population, tournament_size=3):
        """
        锦标赛选择
        
        参数:
            population (list): 解决方案对象列表
            tournament_size (int): 锦标赛大小
            
        返回:
            Solution: 被选中的解决方案
        """
        # 随机选择tournament_size个个体
        candidates = random.sample(population, min(tournament_size, len(population)))
        
        # 选择适应度最好的个体（fitness值最小的）
        best_candidate = min(candidates, key=lambda x: x.fitness)
        
        return best_candidate
    
    @staticmethod
    def roulette_wheel_selection(population):
        """
        轮盘赌选择
        
        参数:
            population (list): 解决方案对象列表
            
        返回:
            Solution: 被选中的解决方案
        """
        # 为了最小化问题，我们需要对适应度值进行转换
        # 找到最大适应度值
        max_fitness = max(sol.fitness for sol in population)
        
        # 计算转换后的适应度（越小的值转换后越大）
        transformed_fitness = [max_fitness - sol.fitness + 1 for sol in population]
        total_fitness = sum(transformed_fitness)
        
        # 计算轮盘赌选择概率
        selection_probs = [fit / total_fitness for fit in transformed_fitness]
        
        # 轮盘赌选择
        return np.random.choice(population, p=selection_probs)
    
    @staticmethod
    def rank_selection(population, selection_pressure=1.5):
        """
        排名选择
        
        参数:
            population (list): 解决方案对象列表
            selection_pressure (float): 选择压力，控制最佳个体和最差个体的选择概率差异
            
        返回:
            Solution: 被选中的解决方案
        """
        # 按适应度对种群排序（升序，因为是最小化问题）
        sorted_population = sorted(population, key=lambda x: x.fitness)
        n = len(sorted_population)
        
        # 计算排名权重
        ranks = np.arange(1, n + 1)
        
        # 使用线性排名选择概率公式
        rank_weights = 2 - selection_pressure + (2 * (selection_pressure - 1) * (n - ranks)) / (n - 1)
        
        # 标准化权重
        total_weight = sum(rank_weights)
        selection_probs = [w / total_weight for w in rank_weights]
        
        # 排名选择
        return np.random.choice(sorted_population, p=selection_probs)
    
    @staticmethod
    def elitism_selection(population, elite_size):
        """
        精英选择
        
        参数:
            population (list): 解决方案对象列表
            elite_size (int): 要选择的精英数量
            
        返回:
            list: 精英解决方案对象列表
        """
        # 按适应度值排序（升序）
        sorted_population = sorted(population, key=lambda x: x.fitness)
        
        # 选择最好的elite_size个体
        elites = sorted_population[:elite_size]
        
        return elites 