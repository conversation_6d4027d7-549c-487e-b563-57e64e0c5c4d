"""
连续热力图可视化模块

此模块用于绘制基于CSV数据的连续热力图，展示不同负载下的各项成本变化。
"""
import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.colors as colors
from matplotlib.ticker import MaxNLocator
import seaborn as sns
from scipy.interpolate import interp1d

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置中文字体支持和字体大小
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 14  # 设置默认字体大小
plt.rcParams['axes.labelsize'] = 16  # 坐标轴标签字体大小
plt.rcParams['axes.titlesize'] = 18  # 标题字体大小
plt.rcParams['xtick.labelsize'] = 14  # x轴刻度标签字体大小
plt.rcParams['ytick.labelsize'] = 14  # y轴刻度标签字体大小
plt.rcParams['legend.fontsize'] = 14  # 图例字体大小


def load_data(file_path):
    """
    加载CSV数据文件

    参数:
        file_path (str): CSV文件路径

    返回:
        pandas.DataFrame: 加载的数据
    """
    try:
        # 尝试直接读取CSV文件
        df = pd.read_csv(file_path, encoding='utf-8')
    except UnicodeDecodeError:
        # 如果出现编码错误，尝试使用不同的编码
        try:
            df = pd.read_csv(file_path, encoding='gbk')
        except UnicodeDecodeError:
            # 再次尝试使用另一种常见的中文编码
            df = pd.read_csv(file_path, encoding='gb18030')

    # 检查并清理列名中的BOM标记
    if '\ufeff' in df.columns[0]:
        df.rename(columns={df.columns[0]: df.columns[0].replace('\ufeff', '')}, inplace=True)

    # 清理列名中的空格
    df.columns = [col.replace(' ', '') for col in df.columns]

    return df


def plot_continuous_heatmap(data, data2=None, output_dir=None, show=True):
    """
    绘制热力图和气泡图

    参数:
        data (DataFrame): 包含负载和成本数据的DataFrame
        data2 (DataFrame, optional): 包含负载、总成本和行驶距离的DataFrame
        output_dir (str, optional): 输出目录
        show (bool): 是否显示图表

    返回:
        str: 保存的文件路径
    """
    # 创建输出目录
    if output_dir is None:
        output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                                 "results", "heatmaps")
    os.makedirs(output_dir, exist_ok=True)

    # 获取列名
    columns = data.columns.tolist()

    # 第一列应该是负载数据
    load_column = columns[0]

    # 其余列是成本数据
    cost_columns = columns[1:]

    # 创建一个新的DataFrame用于热力图
    heatmap_data = pd.DataFrame()
    heatmap_data[load_column] = data[load_column]

    # 先收集所有成本数据，用于全局对数变换
    all_cost_values = []
    for col in cost_columns:
        all_cost_values.extend(data[col].values)

    # 确保所有数据为正值（对数变换要求）
    global_min = min(all_cost_values)
    offset = 0
    if global_min <= 0:
        offset = abs(global_min) + 1  # 加1是为了避免对0取对数

    # 创建变换后的数据框
    transformed_data = pd.DataFrame()

    # 对所有成本列应用相同的对数变换
    for col in cost_columns:
        # 应用对数变换: log(x + offset)
        transformed_data[col] = np.log1p(data[col] + offset)

    # 收集所有变换后的值，用于全局归一化
    all_transformed_values = []
    for col in cost_columns:
        all_transformed_values.extend(transformed_data[col].values)

    # 计算变换后的全局最小值和最大值
    transformed_min = min(all_transformed_values)
    transformed_max = max(all_transformed_values)

    # 对变换后的每个成本列进行全局归一化处理
    for col in cost_columns:
        if transformed_max > transformed_min:
            heatmap_data[col] = (transformed_data[col] - transformed_min) / (transformed_max - transformed_min)
        else:
            heatmap_data[col] = 0

    # 转置数据用于热力图
    heatmap_data_transposed = heatmap_data.set_index(load_column).T

    # 创建图形
    plt.figure(figsize=(14, 8))

    # 使用seaborn绘制热力图
    ax = sns.heatmap(heatmap_data_transposed, cmap='YlOrRd',
                     annot=False, cbar_kws={'label': '对数变换后的归一化成本值'})

    # 设置y轴标签水平放置
    plt.yticks(rotation=0, fontsize=14)
    plt.xticks(fontsize=14)

    # 设置坐标轴标签
    plt.xlabel('最大载重/kg', fontsize=16)
    plt.ylabel('成本类型', fontsize=16)

    # 设置标题
    plt.title('不同最大载重下各项成本的热力图分析（全局对数变换）', fontsize=18, pad=20)

    # 设置颜色条标签字体大小
    cbar = ax.collections[0].colorbar
    cbar.ax.tick_params(labelsize=14)
    cbar.set_label('对数变换后的归一化成本值', fontsize=14)

    # 调整布局
    plt.tight_layout()

    # 保存图表
    save_path = os.path.join(output_dir, "heatmap.png")
    plt.savefig(save_path, dpi=300, bbox_inches='tight')

    # 显示图表
    if show:
        plt.show()
    else:
        plt.close()

    # 绘制每种成本随负载变化的曲线图
    plt.figure(figsize=(14, 8))

    # 使用原始数据（未归一化）
    for col in cost_columns:
        plt.plot(data[load_column], data[col], marker='o', linewidth=2, label=col)

    # 设置坐标轴标签
    plt.xlabel('最大载重/kg', fontsize=16)
    plt.ylabel('成本/元', fontsize=16)

    # 设置标题
    plt.title('不同最大载重下各项成本变化趋势', fontsize=18, pad=20)

    # 添加图例
    plt.legend(loc='best', fontsize=14)

    # 设置坐标轴刻度字体大小
    plt.xticks(fontsize=14)
    plt.yticks(fontsize=14)

    # 添加网格
    plt.grid(True, linestyle='--', alpha=0.7)

    # 设置x轴刻度为整数
    plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))

    # 调整布局
    plt.tight_layout()

    # 保存图表
    trend_save_path = os.path.join(output_dir, "cost_trends.png")
    plt.savefig(trend_save_path, dpi=300, bbox_inches='tight')

    # 显示图表
    if show:
        plt.show()
    else:
        plt.close()

    # 如果提供了第二个数据集，绘制气泡图
    if data2 is not None:
        # 创建气泡图
        plt.figure(figsize=(12, 9))

        # 获取列名
        battery_col = data2.columns[0]  # 电池容量
        cost_col = data2.columns[1]  # 总成本
        distance_col = data2.columns[2]  # 行驶距离
        utilization_col = data2.columns[3]  # 载重利用率

        # 计算气泡大小（根据总成本）
        # 使用更大的气泡大小范围
        min_cost = data2[cost_col].min()
        max_cost = data2[cost_col].max()
        # 极大增加气泡的整体大小
        bubble_sizes = 3000 + 6000 * (data2[cost_col] - min_cost) / (max_cost - min_cost)

        # 获取数据范围，用于设置坐标轴限制
        x_min, x_max = data2[battery_col].min(), data2[battery_col].max()
        y_min, y_max = data2[distance_col].min(), data2[distance_col].max()

        # 处理载重利用率数据（转换为浮点数）
        utilization_values = []
        for val in data2[utilization_col]:
            if isinstance(val, str):
                # 如果是字符串（如"61.40%"），去掉百分号并转换为浮点数
                utilization_values.append(float(val.strip('%')))
            else:
                utilization_values.append(float(val))

        # 创建自定义颜色映射，从蓝色到绿色，降低饱和度
        from matplotlib.colors import LinearSegmentedColormap
        custom_cmap = LinearSegmentedColormap.from_list(
            'BlueToGreen',
            [(0.3, 0.3, 0.7),    # 淡蓝色（最小值）
             (0.2, 0.5, 0.7),    # 蓝色
             (0.2, 0.6, 0.6),    # 蓝绿色
             (0.2, 0.7, 0.5),    # 绿蓝色
             (0.2, 0.6, 0.3)],   # 淡绿色（最大值）
            N=100
        )

        # 绘制气泡图
        scatter = plt.scatter(
            data2[battery_col],
            data2[distance_col],
            s=bubble_sizes,
            c=utilization_values,
            cmap=custom_cmap,  # 使用自定义颜色映射
            alpha=0.9,
            edgecolors='w',
            linewidth=1.5,
            vmin=min(utilization_values) * 0.9,  # 进一步降低最小值，使最浅色更深
            vmax=max(utilization_values) * 1.1   # 提高最大值
        )

        # 添加颜色条
        cbar = plt.colorbar(scatter)
        cbar.set_label('载重利用率/%', fontsize=14)
        cbar.ax.tick_params(labelsize=14)

        # 为每个气泡添加标签，显示总成本数值
        for _, row in data2.iterrows():
            plt.annotate(
                f"{row[cost_col]:.0f}",
                (row[battery_col], row[distance_col]),
                xytext=(0, 0),
                textcoords='offset points',
                fontsize=14,  # 增大文字大小
                ha='center',
                va='center',
                color='white',
                weight='bold'
            )

        # 设置坐标轴范围，确保气泡完全在坐标轴范围内
        # 计算更宽松的边距，延长横坐标左右两侧和纵坐标上下两侧
        x_margin_left = (x_max - x_min) * 0.15  # 左侧边距
        x_margin_right = (x_max - x_min) * 0.15  # 右侧边距
        y_margin_bottom = (y_max - y_min) * 0.15  # 底部边距增大
        y_margin_top = (y_max - y_min) * 0.15  # 顶部边距

        # 设置坐标轴范围
        plt.xlim(x_min - x_margin_left, x_max + x_margin_right)
        plt.ylim(y_min - y_margin_bottom, y_max + y_margin_top)

        # 设置坐标轴标签（根据实际列名动态设置）
        plt.xlabel(battery_col, fontsize=16)
        plt.ylabel(distance_col, fontsize=16)

        # 设置标题
        plt.title(f'{battery_col}、{distance_col}、{cost_col}与{utilization_col}多变量关系图', fontsize=18, pad=20)

        # 设置坐标轴刻度字体大小
        plt.xticks(fontsize=14)
        plt.yticks(fontsize=14)

        # 设置x轴刻度为整数
        plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))

        # 设置y轴刻度间隔更小
        from matplotlib.ticker import MultipleLocator
        # 计算合适的y轴刻度间隔（总范围的约1/8）
        y_interval = (y_max - y_min) / 8
        # 四舍五入到最接近的10的倍数
        y_interval = round(y_interval / 10) * 10
        if y_interval < 10:  # 确保最小间隔为10
            y_interval = 10
        plt.gca().yaxis.set_major_locator(MultipleLocator(y_interval))

        # 调整布局
        plt.tight_layout()

        # 保存图表
        bubble_save_path = os.path.join(output_dir, "bubble_chart.png")
        plt.savefig(bubble_save_path, dpi=300, bbox_inches='tight')

        # 显示图表
        if show:
            plt.show()
        else:
            plt.close()

    return save_path


def main():
    """
    主函数
    """
    # 设置数据文件路径
    data_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                            "data", "可视化数据.csv")
    data_file2 = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                             "data", "可视化数据2.csv")

    # 加载数据
    data = load_data(data_file)

    # 打印数据信息
    print("数据1概览:")
    print(data.head())

    # 加载第二个数据文件（用于气泡图）
    try:
        data2 = load_data(data_file2)
        print("\n数据2概览:")
        print(data2.head())
    except Exception as e:
        print(f"\n无法加载第二个数据文件: {e}")
        data2 = None

    # 绘制热力图和气泡图
    output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                             "results", "heatmaps")
    save_path = plot_continuous_heatmap(data, data2, output_dir=output_dir, show=True)

    print(f"\n图表已保存至: {os.path.dirname(save_path)}")


if __name__ == "__main__":
    main()
