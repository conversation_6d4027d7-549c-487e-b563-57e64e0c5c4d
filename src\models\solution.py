"""
解决方案模型，用于表示和评估配送路径解决方案
"""
import numpy as np
import copy


class Solution:
    def __init__(self, vehicles, customers, charging_stations, distance_matrix):
        """
        初始化解决方案

        参数:
            vehicles (list): 车辆对象列表
            customers (list): 客户对象列表
            charging_stations (list): 充电站对象列表
            distance_matrix (array): 距离矩阵 [depot+customers+charging_stations]
        """
        self.vehicles = copy.deepcopy(vehicles)
        self.customers = customers  # 引用即可，不需要深拷贝
        self.charging_stations = charging_stations  # 同上
        self.distance_matrix = distance_matrix

        # 解决方案表示
        self.chromosome = []  # 基因表示
        self.vehicle_routes = []  # 每辆车的路径

        # 评估结果
        self.fitness = float('inf')
        self.fixed_cost = 0
        self.distance_cost = 0
        self.charging_cost = 0
        self.refrigeration_cost = 0
        self.temp_violation_cost = 0
        self.refr_startup_cost = 0
        self.multi_temp_zone_cost = 0
        self.time_penalty_cost = 0
        self.early_arrival_penalty = 0  # 提前到达惩罚
        self.late_arrival_penalty = 0   # 延误到达惩罚
        self.maintenance_cost = 0
        self.damage_cost = 0
        self.is_feasible = False

        # 重置车辆状态
        for vehicle in self.vehicles:
            vehicle.reset()

    def encode(self, routes):
        """
        将路径编码为染色体

        参数:
            routes (list): 车辆路径列表，每个元素是一辆车访问的节点ID列表
                          节点ID: 0表示配送中心，1~n表示客户，n+1及以上表示充电站

        返回:
            list: 染色体
        """
        # 记录路径
        self.vehicle_routes = copy.deepcopy(routes)

        # 初始化染色体
        chromosome = []

        # 提取客户节点（1到n）
        customer_nodes = []
        for route in routes:
            for node in route:
                if node >= 1 and node <= len(self.customers):
                    customer_nodes.append(node)

        # 首先添加所有客户节点
        chromosome.extend(customer_nodes)

        # 然后添加车辆分隔符（使用大于客户数量的值）
        n_customers = len(self.customers)
        vehicle_delimiters = []

        current_idx = 0
        for route in routes:
            # 获取当前路径中的客户节点
            route_customers = [node for node in route if node >= 1 and node <= n_customers]

            if len(route_customers) > 0:
                # 计算最后一个客户的位置
                current_idx += len(route_customers)
                # 添加车辆分隔符（指向最后一个客户的索引）
                vehicle_delimiters.append(current_idx + n_customers)

        # 添加车辆分隔符到染色体
        if vehicle_delimiters:
            # 移除最后一个分隔符（最后一辆车不需要分隔符）
            vehicle_delimiters.pop()
            chromosome.extend(vehicle_delimiters)

        self.chromosome = chromosome
        return self.chromosome

    def decode(self, chromosome=None):
        """
        将染色体解码为路径

        参数:
            chromosome (list, optional): 染色体。如果未提供，则使用当前染色体。

        返回:
            list: 车辆路径列表
        """
        if chromosome is not None:
            self.chromosome = chromosome

        # 如果染色体为空，则返回空路径
        if not self.chromosome:
            self.vehicle_routes = []
            return self.vehicle_routes

        # 提取客户节点和车辆分隔符
        n_customers = len(self.customers)
        customer_genes = [gene for gene in self.chromosome if gene <= n_customers]
        delimiter_genes = [gene for gene in self.chromosome if gene > n_customers]

        # 计算分隔点
        split_points = []
        for gene in delimiter_genes:
            adjusted_idx = gene - n_customers - 1
            if 0 <= adjusted_idx < len(customer_genes):
                split_points.append(adjusted_idx + 1)

        # 确保分隔点是有序的
        split_points.sort()

        # 分割客户序列为多个子路径
        customer_routes = []
        start_idx = 0

        for end_idx in split_points:
            customer_routes.append(customer_genes[start_idx:end_idx])
            start_idx = end_idx

        # 添加最后一段
        if start_idx < len(customer_genes):
            customer_routes.append(customer_genes[start_idx:])

        # 构建完整路径（添加配送中心和必要的充电站）
        complete_routes = self._build_complete_routes(customer_routes)

        self.vehicle_routes = complete_routes
        return self.vehicle_routes

    def _build_complete_routes(self, customer_routes):
        """
        构建完整路径，包括配送中心和必要的充电站

        参数:
            customer_routes (list): 客户路径列表

        返回:
            list: 完整路径列表
        """
        # 重置所有车辆状态
        for vehicle in self.vehicles:
            vehicle.reset()

        # 初始化完整路径
        complete_routes = []

        # 处理每条客户路径
        for v_idx, customer_route in enumerate(customer_routes):
            if v_idx >= len(self.vehicles):
                break  # 如果路径数超过车辆数，则忽略多余的路径

            vehicle = self.vehicles[v_idx]

            # 初始化当前路径，从配送中心出发
            current_route = [0]  # 0表示配送中心
            current_position = 0
            current_time = 0  # 当前时间（从0开始）

            # 初始化车辆在配送中心的调度信息
            vehicle.schedule = [{
                'node_id': 0,
                'arrival_time': 0,
                'waiting_time': 0,
                'service_time': 0,
                'departure_time': 0
            }]

            # 访问每个客户
            for customer_id in customer_route:
                # 客户对象
                customer = self.customers[customer_id - 1]

                # 计算从当前位置到客户的距离
                distance_to_customer = self.distance_matrix[current_position][customer_id]

                # 计算所需电量
                energy_required = distance_to_customer * vehicle.consumption_rate

                # 检查是否需要充电
                if vehicle.current_battery < energy_required:
                    # 寻找最近的充电站
                    nearest_station, station_id, distance_to_station = self._find_nearest_charging_station(current_position)

                    # 计算去充电站需要的电量
                    energy_to_station = distance_to_station * vehicle.consumption_rate

                    # 检查是否能到达充电站
                    if vehicle.current_battery >= energy_to_station:
                        # 添加充电站到路径
                        current_route.append(station_id)

                        # 更新车辆状态（行驶到充电站）
                        travel_time = vehicle.update_after_travel(station_id, distance_to_station)
                        current_time += travel_time

                        # 计算从充电站出发后所需的电量
                        distance_station_to_customer = self.distance_matrix[station_id][customer_id]
                        energy_station_to_customer = distance_station_to_customer * vehicle.consumption_rate

                        # 计算完成剩余行程所需的最小电量
                        distance_customer_to_depot = self.distance_matrix[customer_id][0]
                        energy_customer_to_depot = distance_customer_to_depot * vehicle.consumption_rate

                        # 需要的总电量（去客户+从客户回配送中心）
                        total_energy_needed = energy_station_to_customer + energy_customer_to_depot

                        # 计算充电所需电量
                        energy_to_charge = min(
                            total_energy_needed,
                            vehicle.battery_capacity * vehicle.partial_charging_factor - vehicle.current_battery
                        )

                        # 如果当前电量已经超过目标充电水平，则不需要充电
                        if vehicle.current_battery >= vehicle.battery_capacity * vehicle.partial_charging_factor:
                            energy_to_charge = 0
                            charging_time = 0
                        else:
                            # 计算充电时间（包括排队时间）
                            pure_charging_time = energy_to_charge / nearest_station.charging_rate
                            charging_time = pure_charging_time + nearest_station.queue_time

                        # 更新车辆电量和时间
                        vehicle.update_after_charging(charging_time, nearest_station.charging_rate, nearest_station.queue_time)
                        current_time += charging_time

                        # 继续到客户
                        current_route.append(customer_id)
                        travel_time = vehicle.update_after_travel(customer_id, distance_station_to_customer, customer)

                        # 更新当前时间（包括等待和服务时间）
                        last_schedule = vehicle.schedule[-1]
                        current_time = last_schedule['departure_time']

                        current_position = customer_id
                    else:
                        # 无法到达最近的充电站，路径不可行
                        # 尝试直接回配送中心
                        distance_to_depot = self.distance_matrix[current_position][0]
                        energy_to_depot = distance_to_depot * vehicle.consumption_rate

                        if vehicle.current_battery >= energy_to_depot:
                            current_route.append(0)
                            vehicle.update_after_travel(0, distance_to_depot)

                        # 跳过剩余客户
                        break
                else:
                    # 直接访问客户
                    current_route.append(customer_id)
                    travel_time = vehicle.update_after_travel(customer_id, distance_to_customer, customer)

                    # 更新当前时间（包括等待和服务时间）
                    last_schedule = vehicle.schedule[-1]
                    current_time = last_schedule['departure_time']

                    current_position = customer_id

            # 检查是否能够返回配送中心
            if current_position != 0:  # 如果不在配送中心
                distance_to_depot = self.distance_matrix[current_position][0]
                energy_to_depot = distance_to_depot * vehicle.consumption_rate

                if vehicle.current_battery < energy_to_depot:
                    # 需要找充电站
                    nearest_station, station_id, distance_to_station = self._find_nearest_charging_station(current_position)

                    # 检查是否能到达充电站
                    energy_to_station = distance_to_station * vehicle.consumption_rate

                    if vehicle.current_battery >= energy_to_station:
                        # 添加充电站到路径
                        current_route.append(station_id)

                        # 更新车辆状态
                        travel_time = vehicle.update_after_travel(station_id, distance_to_station)
                        current_time += travel_time

                        # 计算从充电站到配送中心的距离和能耗
                        distance_station_to_depot = self.distance_matrix[station_id][0]
                        energy_station_to_depot = distance_station_to_depot * vehicle.consumption_rate

                        # 计算充电所需电量
                        energy_to_charge = min(
                            energy_station_to_depot,
                            vehicle.battery_capacity * vehicle.partial_charging_factor - vehicle.current_battery
                        )

                        # 如果当前电量已经超过目标充电水平，则不需要充电
                        if vehicle.current_battery >= vehicle.battery_capacity * vehicle.partial_charging_factor:
                            energy_to_charge = 0
                            charging_time = 0
                        else:
                            # 计算充电时间（包括排队时间）
                            pure_charging_time = energy_to_charge / nearest_station.charging_rate
                            charging_time = pure_charging_time + nearest_station.queue_time

                        # 更新车辆电量
                        vehicle.update_after_charging(charging_time, nearest_station.charging_rate, nearest_station.queue_time)
                        current_time += charging_time

                        # 返回配送中心
                        current_route.append(0)
                        vehicle.update_after_travel(0, distance_station_to_depot)
                    else:
                        # 无法到达最近的充电站，路径不可行
                        self.is_feasible = False
                else:
                    # 直接返回配送中心
                    current_route.append(0)
                    vehicle.update_after_travel(0, distance_to_depot)

            # 如果路径只包含配送中心，则添加回配送中心的路径
            if len(current_route) == 1:
                current_route.append(0)

            # 添加完整路径
            complete_routes.append(current_route)

        # 为未使用的车辆添加空路径
        for _ in range(len(self.vehicles) - len(complete_routes)):
            complete_routes.append([0, 0])

        # 检查所有客户是否都被服务
        all_customers_served = set()
        for route in complete_routes:
            for node in route:
                if node >= 1 and node <= len(self.customers):
                    all_customers_served.add(node)

        self.is_feasible = len(all_customers_served) == len(self.customers)

        return complete_routes

    def _find_nearest_charging_station(self, position):
        """
        找到距离当前位置最近的充电站

        参数:
            position (int): 当前位置的ID

        返回:
            tuple: (充电站对象, 充电站ID, 距离)
        """
        n_customers = len(self.customers)
        min_distance = float('inf')
        nearest_station = None
        station_id = -1

        for i, station in enumerate(self.charging_stations):
            station_idx = i + n_customers + 1
            distance = self.distance_matrix[position][station_idx]

            if distance < min_distance:
                min_distance = distance
                nearest_station = station
                station_id = station_idx

        return nearest_station, station_id, min_distance

    def evaluate(self, cost_params):
        """
        评估解决方案，计算适应度

        参数:
            cost_params (dict): 成本参数

        返回:
            float: 适应度值（总成本）
        """
        # 初始化成本
        self.fixed_cost = 0
        self.distance_cost = 0
        self.charging_cost = 0
        self.refrigeration_cost = 0
        self.temp_violation_cost = 0
        self.refr_startup_cost = 0
        self.multi_temp_zone_cost = 0
        self.time_penalty_cost = 0
        self.maintenance_cost = 0
        self.damage_cost = 0
        self.early_arrival_penalty = 0
        self.late_arrival_penalty = 0

        # 检查车辆载重约束
        for vehicle in self.vehicles:
            if vehicle.current_load > vehicle.max_load:
                # 如果任何车辆的载重超过最大载重，则解决方案不可行
                self.is_feasible = False
                break

        # 如果解决方案不可行，则返回一个很大的值
        if not self.is_feasible:
            self.fitness = float('inf')
            return self.fitness

        # 计算固定成本 - 使用的车辆数量乘以每辆车的固定成本
        used_vehicles = sum(1 for route in self.vehicle_routes if len(route) > 2)
        self.fixed_cost = used_vehicles * cost_params['fixed_cost_per_vehicle']

        # 距离成本
        total_distance = sum(vehicle.total_distance for vehicle in self.vehicles if vehicle.total_distance > 0)
        self.distance_cost = total_distance * cost_params['distance_cost_per_km']

        # 充电成本 - 根据充电量计算
        total_charged_energy = 0
        for i, vehicle in enumerate(self.vehicles):
            if vehicle.total_distance > 0 and vehicle.total_charging_time > 0:
                # 获取该车辆的路径
                route = self.vehicle_routes[i] if i < len(self.vehicle_routes) else []

                # 查找路径中的充电站
                charging_stations_used = []
                for node_id in route:
                    if node_id > len(self.customers):
                        station_idx = node_id - len(self.customers) - 1
                        if station_idx < len(self.charging_stations):
                            charging_stations_used.append(self.charging_stations[station_idx])

                if charging_stations_used:
                    # 如果使用了多个充电站，按比例分配充电时间
                    # 这里简化处理，假设每个充电站充电时间相等
                    charging_time_per_station = vehicle.total_charging_time / len(charging_stations_used)

                    # 根据每个充电站的实际充电速率计算充电量
                    for station in charging_stations_used:
                        # 从总充电时间中减去排队时间，得到实际充电时间
                        actual_charging_time = charging_time_per_station - station.queue_time
                        if actual_charging_time > 0:
                            charging_rate = station.charging_rate  # 使用实际充电站的充电速率
                            total_charged_energy += actual_charging_time * charging_rate
                else:
                    # 如果找不到使用的充电站（异常情况），使用默认值
                    print(f"警告: 车辆{vehicle.id}有充电时间但在路径中找不到充电站，使用默认充电速率1.0 kWh/min")
                    total_charged_energy += vehicle.total_charging_time * 1.0

        self.charging_cost = total_charged_energy * cost_params['charging_cost_per_kwh']

        # 制冷成本计算 - 基于新的参数进行计算
        for vehicle in self.vehicles:
            # 计算空载和装载时间
            idle_time_hours = 0.0  # 空载时间(小时)
            loading_time_hours = 0.0  # 装载时间(小时)

            # 总运行时间(小时)
            total_operation_time = vehicle.total_travel_time / 60.0

            # 计算装载时间比例 - 根据当前载重与最大载重的比例估算
            if vehicle.total_travel_time > 0:
                loading_ratio = sum(customer.demand for customer in self.customers if customer.id in vehicle.route) / vehicle.max_load
                loading_time_hours = total_operation_time * loading_ratio
                idle_time_hours = total_operation_time * (1 - loading_ratio)

            # 空载和装载的制冷成本
            idle_refrigeration_cost = idle_time_hours * cost_params['refrigeration_cost_idle']
            loading_refrigeration_cost = loading_time_hours * cost_params['refrigeration_cost_loading']
            self.refrigeration_cost += (idle_refrigeration_cost + loading_refrigeration_cost)

            # 维护成本
            idle_maintenance_cost = idle_time_hours * cost_params['maintenance_cost_idling']
            loading_maintenance_cost = loading_time_hours * cost_params['maintenance_cost_loading']
            self.maintenance_cost += (idle_maintenance_cost + loading_maintenance_cost)

            # 制冷系统启停成本
            if 'refrigeration_startup_cost' in cost_params:
                self.refr_startup_cost += vehicle.refr_state_changes * cost_params['refrigeration_startup_cost']

            # 温度违规惩罚成本
            if 'temp_violation_penalty' in cost_params:
                for violation in vehicle.temperature_violations:
                    customer_id, violation_degree, actual_temp = violation
                    self.temp_violation_cost += violation_degree * cost_params['temp_violation_penalty']

            # 多温区额外成本
            if vehicle.multi_temp_zones and 'multi_temp_zone_cost' in cost_params:
                active_zones = sum(1 for zone, customers in vehicle.temp_zone_customers.items() if customers)
                if active_zones > 1:
                    # 只有当车辆服务多个温区的客户时才计算额外成本
                    self.multi_temp_zone_cost += (total_operation_time * cost_params['multi_temp_zone_cost'] * (active_zones - 1))

        # 时间惩罚成本直接计算提前到达惩罚和延误到达惩罚

        # 时间惩罚成本 (提前到达和延迟到达)
        # 注意：此部分需要客户时间窗口数据的支持
        self.early_arrival_penalty = 0  # 重置提前到达惩罚
        self.late_arrival_penalty = 0   # 重置延误到达惩罚

        for vehicle in self.vehicles:
            if not hasattr(vehicle, 'schedule'):
                continue

            # 使用节点ID而不是索引来匹配schedule和route
            for schedule_item in vehicle.schedule:
                node_id = schedule_item['node_id']
                if node_id > 0 and node_id <= len(self.customers):  # 确保是客户节点
                    customer = self.customers[node_id - 1]
                    arrival_time = schedule_item['arrival_time']

                    if arrival_time < customer.time_window[0]:  # 提前到达
                        early_hours = (customer.time_window[0] - arrival_time) / 60.0
                        early_penalty = early_hours * cost_params['time_penalty_early']
                        self.early_arrival_penalty += early_penalty
                    elif arrival_time > customer.time_window[1]:  # 延迟到达
                        late_hours = (arrival_time - customer.time_window[1]) / 60.0
                        late_penalty = late_hours * cost_params['time_penalty_late']
                        self.late_arrival_penalty += late_penalty

        # 总时间惩罚成本 = 提前到达惩罚 + 延误到达惩罚
        self.time_penalty_cost = self.early_arrival_penalty + self.late_arrival_penalty

        # 货物损坏成本 - 基于装卸操作次数
        total_demand = sum(customer.demand for customer in self.customers if customer.id in [node_id for vehicle in self.vehicles for node_id in vehicle.route])
        loading_damage = total_demand * (cost_params['damage_rate_loading'] / 100.0)
        unloading_damage = total_demand * (cost_params['damage_rate_unloading'] / 100.0)
        self.damage_cost = loading_damage + unloading_damage

        # 计算总成本（适应度）
        self.fitness = (
            self.fixed_cost +
            self.distance_cost +
            self.charging_cost +
            self.refrigeration_cost +
            self.temp_violation_cost +
            self.refr_startup_cost +
            self.multi_temp_zone_cost +
            self.time_penalty_cost +
            self.maintenance_cost +
            self.damage_cost
        )

        return self.fitness

    def __str__(self):
        """字符串表示"""
        if not self.is_feasible:
            return "不可行解决方案"

        used_vehicles = sum(1 for route in self.vehicle_routes if len(route) > 2)

        result = f"解决方案 (适应度={self.fitness:.2f}):\n"
        result += f"  使用车辆数: {used_vehicles}/{len(self.vehicles)}\n"
        result += f"  总成本: {self.fitness:.2f}\n"
        result += f"    - 固定成本: {self.fixed_cost:.2f}\n"
        result += f"    - 距离成本: {self.distance_cost:.2f}\n"
        result += f"    - 充电成本: {self.charging_cost:.2f}\n"
        result += f"    - 制冷成本: {self.refrigeration_cost:.2f}\n"

        # 添加新增成本项
        if hasattr(self, 'early_arrival_penalty') and self.early_arrival_penalty > 0:
            result += f"    - 提前到达惩罚: {self.early_arrival_penalty:.2f}\n"
        if hasattr(self, 'late_arrival_penalty') and self.late_arrival_penalty > 0:
            result += f"    - 延误到达惩罚: {self.late_arrival_penalty:.2f}\n"
        if hasattr(self, 'maintenance_cost') and self.maintenance_cost > 0:
            result += f"    - 维护成本: {self.maintenance_cost:.2f}\n"
        if hasattr(self, 'damage_cost') and self.damage_cost > 0:
            result += f"    - 货物损坏成本: {self.damage_cost:.2f}\n"
        if hasattr(self, 'temp_violation_cost') and self.temp_violation_cost > 0:
            result += f"    - 温度违规成本: {self.temp_violation_cost:.2f}\n"
        if hasattr(self, 'refr_startup_cost') and self.refr_startup_cost > 0:
            result += f"    - 制冷启停成本: {self.refr_startup_cost:.2f}\n"
        if hasattr(self, 'multi_temp_zone_cost') and self.multi_temp_zone_cost > 0:
            result += f"    - 多温区成本: {self.multi_temp_zone_cost:.2f}\n"

        result += "  路径:\n"
        for i, route in enumerate(self.vehicle_routes):
            if len(route) <= 2:  # 跳过空路径
                continue

            result += f"    车辆 {i+1}: "
            for node in route:
                if node == 0:
                    result += "配送中心 -> "
                elif 1 <= node <= len(self.customers):
                    result += f"客户{node} -> "
                else:
                    station_idx = node - len(self.customers) - 1
                    result += f"充电站{self.charging_stations[station_idx].id} -> "
            result = result[:-4] + "\n"  # 移除最后的箭头

        return result