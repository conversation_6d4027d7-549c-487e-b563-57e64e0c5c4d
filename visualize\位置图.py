import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """加载数据文件"""
    # 读取客户数据
    customers_df = pd.read_csv('data/customers.csv')
    # 读取充电站数据
    charging_stations_df = pd.read_csv('data/charging_stations.csv')

    return customers_df, charging_stations_df

def plot_locations():
    """绘制配送中心、客户点和充电站位置图"""
    customers_df, charging_stations_df = load_data()

    # 创建图形
    plt.figure(figsize=(12, 10))

    # 分离配送中心和客户点
    depot = customers_df[customers_df['id'] == 0]  # 配送中心
    customers = customers_df[customers_df['id'] != 0]  # 客户点

    # 绘制配送中心
    plt.scatter(depot['x'], depot['y'],
               c='red', s=120, marker='s',
               label='配送中心', zorder=5, edgecolors='black', linewidth=1.5)

    # 绘制客户点
    plt.scatter(customers['x'], customers['y'],
               c='lightblue', s=80, marker='o',
               label='客户点', zorder=3, edgecolors='blue', linewidth=1, alpha=0.8)

    # 绘制充电站
    plt.scatter(charging_stations_df['x'], charging_stations_df['y'],
               c='green', s=100, marker='^',
               label='充电站', zorder=4, edgecolors='darkgreen', linewidth=1.5)

    # 设置图形属性
    plt.xlabel('X坐标', fontsize=12)
    plt.ylabel('Y坐标', fontsize=12)
    plt.title('配送网络位置图', fontsize=16, fontweight='bold', pad=20)
    plt.legend(fontsize=12, loc='upper right')
    plt.grid(True, alpha=0.3)

    # 设置坐标轴范围，减小边距
    all_x = list(customers_df['x']) + list(charging_stations_df['x'])
    all_y = list(customers_df['y']) + list(charging_stations_df['y'])

    x_margin = (max(all_x) - min(all_x)) * 0.05  # 减小x轴边距
    y_margin = (max(all_y) - min(all_y)) * 0.08  # 减小y轴边距

    plt.xlim(min(all_x) - x_margin, max(all_x) + x_margin)
    plt.ylim(min(all_y) - y_margin, max(all_y) + y_margin)

    # 设置等比例坐标轴
    plt.axis('equal')

    plt.tight_layout()
    plt.show()



def print_summary():
    """打印数据摘要信息"""
    customers_df, charging_stations_df = load_data()

    print("=" * 50)
    print("配送网络数据摘要")
    print("=" * 50)
    print(f"配送中心数量: 1")
    print(f"客户点数量: {len(customers_df) - 1}")  # 减去配送中心
    print(f"充电站数量: {len(charging_stations_df)}")
    print()

    print("配送中心位置:")
    depot = customers_df[customers_df['id'] == 0]
    print(f"  坐标: ({depot['x'].iloc[0]}, {depot['y'].iloc[0]})")
    print()

    print("充电站信息:")
    for _, row in charging_stations_df.iterrows():
        print(f"  CS{int(row['id'])}: 坐标({row['x']}, {row['y']}), "
              f"充电速率: {row['charging_rate']} kWh/min, "
              f"排队时间: {row['queue_time']} min")
    print()

    print("客户点范围:")
    customers = customers_df[customers_df['id'] != 0]
    print(f"  X坐标范围: {customers['x'].min()} - {customers['x'].max()}")
    print(f"  Y坐标范围: {customers['y'].min()} - {customers['y'].max()}")
    print(f"  总需求量: {customers['demand'].sum()} kg")

if __name__ == "__main__":
    # 打印数据摘要
    print_summary()

    # 绘制位置图
    print("\n正在生成位置图...")
    plot_locations()
