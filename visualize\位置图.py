import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os

# 设置中文字体和字体大小
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 15  # 设置默认字体大小
plt.rcParams['axes.labelsize'] = 17  # 坐标轴标签字体大小
plt.rcParams['axes.titlesize'] = 19  # 标题字体大小
plt.rcParams['xtick.labelsize'] = 15  # x轴刻度标签字体大小
plt.rcParams['ytick.labelsize'] = 15  # y轴刻度标签字体大小
plt.rcParams['legend.fontsize'] = 15  # 图例字体大小

def load_data():
    """加载数据文件"""
    # 读取客户数据
    customers_df = pd.read_csv('data/customers.csv')
    # 读取充电站数据
    charging_stations_df = pd.read_csv('data/charging_stations.csv')

    return customers_df, charging_stations_df

def plot_locations():
    """绘制配送中心、客户点和充电站位置图"""
    customers_df, charging_stations_df = load_data()

    # 创建图形
    plt.figure(figsize=(12, 10))

    # 分离配送中心和客户点
    depot = customers_df[customers_df['id'] == 0]  # 配送中心
    customers = customers_df[customers_df['id'] != 0]  # 客户点

    # 绘制配送中心
    plt.scatter(depot['x'], depot['y'],
               c='red', s=120, marker='s',
               label='配送中心', zorder=5, edgecolors='black', linewidth=1.5)

    # 绘制客户点
    plt.scatter(customers['x'], customers['y'],
               c='lightblue', s=80, marker='o',
               label='客户点', zorder=3, edgecolors='blue', linewidth=1, alpha=0.8)

    # 绘制充电站
    plt.scatter(charging_stations_df['x'], charging_stations_df['y'],
               c='green', s=100, marker='^',
               label='充电站', zorder=4, edgecolors='darkgreen', linewidth=1.5)

    # 设置图形属性
    plt.xlabel('X坐标', fontsize=12)
    plt.ylabel('Y坐标', fontsize=12)
    plt.title('配送网络位置图', fontsize=16, fontweight='bold', pad=20)
    plt.legend(fontsize=12, loc='upper right')
    plt.grid(True, alpha=0.3)

    # 设置坐标轴范围
    all_y = list(customers_df['y']) + list(charging_stations_df['y'])

    y_margin = (max(all_y) - min(all_y)) * 0.05  # Y轴边距

    # 直接设置坐标轴范围，不使用等比例以保持指定范围
    plt.xlim(0, 70)  # 直接指定X轴范围
    plt.ylim(min(all_y) - y_margin, max(all_y) + y_margin)

    plt.tight_layout()

    # 确保images目录存在
    images_dir = 'images'
    if not os.path.exists(images_dir):
        os.makedirs(images_dir)

    # 保存图片
    plt.savefig(os.path.join(images_dir, '配送网络位置图.png'),
                dpi=300, bbox_inches='tight', facecolor='white')
    print(f"图片已保存到: {os.path.join(images_dir, '配送网络位置图.png')}")

    plt.show()

def plot_route_planning():
    """绘制车辆路径规划图"""
    customers_df, charging_stations_df = load_data()

    # 定义车辆路线（根据用户提供的数据）
    vehicle_routes = [
        [0, 20, 33, 3, 50, 1, 31, 10, 30, 32, 0],
        [0, 45, 8, 17, 5, 6, 13, 2, 40, 0],
        [0, 21, 25, 4, 26, 27, 18, 51, 11, 46, 0],  # 51代表充电站[1]
        [0, 7, 36, 47, 48, 19, 49, 0],
        [0, 16, 44, 37, 38, 0],
        [0, 39, 23, 22, 41, 15, 43, 42, 14, 0],
        [0, 9, 35, 34, 29, 24, 12, 28, 0]
    ]

    # 创建图形
    plt.figure(figsize=(14, 12))

    # 分离配送中心和客户点
    depot = customers_df[customers_df['id'] == 0]  # 配送中心
    customers = customers_df[customers_df['id'] != 0]  # 客户点

    # 绘制配送中心
    plt.scatter(depot['x'], depot['y'],
               c='red', s=200, marker='*',
               label='配送中心', zorder=5, edgecolors='black', linewidth=2)

    # 绘制客户点
    plt.scatter(customers['x'], customers['y'],
               c='lightblue', s=80, marker='o',
               label='客户点', zorder=3, edgecolors='blue', linewidth=1, alpha=0.8)

    # 绘制充电站
    plt.scatter(charging_stations_df['x'], charging_stations_df['y'],
               c='green', s=120, marker='^',
               label='充电站', zorder=4, edgecolors='darkgreen', linewidth=1.5)



    # 添加客户点标签
    for _, row in customers.iterrows():
        plt.annotate(f'{int(row["id"])}',
                    xy=(row['x'], row['y']),
                    xytext=(row['x'] + 0.3, row['y'] + 0.3),
                    fontsize=12, color='blue', fontweight='bold')

    # 添加充电站标签
    for _, row in charging_stations_df.iterrows():
        plt.annotate(f'CS{int(row["id"])}',
                    xy=(row['x'], row['y']),
                    xytext=(row['x'] + 0.4, row['y'] + 0.4),
                    fontsize=12, fontweight='bold', color='darkgreen')

    # 定义车辆路线颜色
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']

    # 绘制每辆车的路线
    for vehicle_idx, route in enumerate(vehicle_routes):
        if len(route) <= 2:  # 跳过没有客户的路线
            continue

        color = colors[vehicle_idx % len(colors)]
        route_x = []
        route_y = []

        # 获取路线坐标
        for node in route:
            if node == 0:  # 配送中心
                route_x.append(depot['x'].iloc[0])
                route_y.append(depot['y'].iloc[0])
            elif 1 <= node <= 50:  # 客户点
                customer_row = customers_df[customers_df['id'] == node]
                if not customer_row.empty:
                    route_x.append(customer_row['x'].iloc[0])
                    route_y.append(customer_row['y'].iloc[0])
            elif node >= 51:  # 充电站 (51对应充电站[1], 即id=1)
                station_id = node - 51  # 转换为充电站id
                station_row = charging_stations_df[charging_stations_df['id'] == station_id]
                if not station_row.empty:
                    route_x.append(station_row['x'].iloc[0])
                    route_y.append(station_row['y'].iloc[0])

        # 绘制路线
        if len(route_x) > 1:
            plt.plot(route_x, route_y, color=color, linestyle='-', linewidth=2.5,
                    label=f'车辆 {vehicle_idx+1}', alpha=0.8)

            # 添加箭头指示方向
            for i in range(len(route_x) - 1):
                dx = route_x[i+1] - route_x[i]
                dy = route_y[i+1] - route_y[i]

                # 计算距离，只在较长的线段上添加箭头
                dist = np.sqrt(dx**2 + dy**2)
                if dist > 3.0:
                    # 在线段中间位置添加箭头
                    mid_x = route_x[i] + dx * 0.6
                    mid_y = route_y[i] + dy * 0.6
                    plt.arrow(mid_x - dx * 0.1, mid_y - dy * 0.1, dx * 0.2, dy * 0.2,
                             head_width=1.0, head_length=1.2, fc=color, ec=color, alpha=0.8)

    # 设置图形属性
    plt.xlabel('X坐标', fontsize=17)
    plt.ylabel('Y坐标', fontsize=17)
    plt.title('车辆路径规划图', fontsize=19, fontweight='bold', pad=20)
    plt.legend(fontsize=15, loc='upper right')
    plt.grid(True, alpha=0.3)

    # 设置坐标轴范围
    plt.xlim(0, 70)
    all_y = list(customers_df['y']) + list(charging_stations_df['y'])
    y_margin = (max(all_y) - min(all_y)) * 0.05
    plt.ylim(min(all_y) - y_margin, max(all_y) + y_margin)

    plt.tight_layout()

    # 确保images目录存在
    images_dir = 'images'
    if not os.path.exists(images_dir):
        os.makedirs(images_dir)

    # 保存图片
    plt.savefig(os.path.join(images_dir, '车辆路径规划图.png'),
                dpi=300, bbox_inches='tight', facecolor='white')
    print(f"图片已保存到: {os.path.join(images_dir, '车辆路径规划图.png')}")

    plt.show()



def print_summary():
    """打印数据摘要信息"""
    customers_df, charging_stations_df = load_data()

    print("=" * 50)
    print("配送网络数据摘要")
    print("=" * 50)
    print(f"配送中心数量: 1")
    print(f"客户点数量: {len(customers_df) - 1}")  # 减去配送中心
    print(f"充电站数量: {len(charging_stations_df)}")
    print()

    print("配送中心位置:")
    depot = customers_df[customers_df['id'] == 0]
    print(f"  坐标: ({depot['x'].iloc[0]}, {depot['y'].iloc[0]})")
    print()

    print("充电站信息:")
    for _, row in charging_stations_df.iterrows():
        print(f"  CS{int(row['id'])}: 坐标({row['x']}, {row['y']}), "
              f"充电速率: {row['charging_rate']} kWh/min, "
              f"排队时间: {row['queue_time']} min")
    print()

    print("客户点范围:")
    customers = customers_df[customers_df['id'] != 0]
    print(f"  X坐标范围: {customers['x'].min()} - {customers['x'].max()}")
    print(f"  Y坐标范围: {customers['y'].min()} - {customers['y'].max()}")
    print(f"  总需求量: {customers['demand'].sum()} kg")

if __name__ == "__main__":
    # 打印数据摘要
    print_summary()

    # 绘制位置图
    print("\n正在生成位置图...")
    plot_locations()

    # 绘制路径规划图
    print("\n正在生成路径规划图...")
    plot_route_planning()
