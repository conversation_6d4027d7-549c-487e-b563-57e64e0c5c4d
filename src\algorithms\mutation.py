"""
变异操作模块，实现多种变异策略
"""
import random
import numpy as np
from copy import deepcopy


class MutationOperator:
    """变异操作类，支持多种变异策略"""
    
    @staticmethod
    def swap_mutation(solution, mutation_rate=0.1):
        """
        交换变异：随机交换两个客户的位置
        
        参数:
            solution (Solution): 解决方案对象
            mutation_rate (float): 变异率
            
        返回:
            Solution: 变异后的解决方案
        """
        # 深拷贝解决方案
        mutated_solution = deepcopy(solution)
        
        # 获取客户数量
        n_customers = len(solution.customers)
        
        # 分离客户部分和车辆分隔符部分
        chromosome = mutated_solution.chromosome
        customers = [gene for gene in chromosome if gene <= n_customers]
        delimiters = [gene for gene in chromosome if gene > n_customers]
        
        # 交换变异
        if random.random() < mutation_rate and len(customers) >= 2:
            # 随机选择两个不同位置
            pos1, pos2 = random.sample(range(len(customers)), 2)
            
            # 交换客户
            customers[pos1], customers[pos2] = customers[pos2], customers[pos1]
        
        # 重建染色体
        mutated_chromosome = customers + delimiters
        
        # 解码
        mutated_solution.decode(mutated_chromosome)
        
        return mutated_solution
    
    @staticmethod
    def insert_mutation(solution, mutation_rate=0.1):
        """
        插入变异：随机选择一个客户，将其插入到另一个位置
        
        参数:
            solution (Solution): 解决方案对象
            mutation_rate (float): 变异率
            
        返回:
            Solution: 变异后的解决方案
        """
        # 深拷贝解决方案
        mutated_solution = deepcopy(solution)
        
        # 获取客户数量
        n_customers = len(solution.customers)
        
        # 分离客户部分和车辆分隔符部分
        chromosome = mutated_solution.chromosome
        customers = [gene for gene in chromosome if gene <= n_customers]
        delimiters = [gene for gene in chromosome if gene > n_customers]
        
        # 插入变异
        if random.random() < mutation_rate and len(customers) >= 2:
            # 随机选择要插入的客户位置
            from_pos = random.randrange(len(customers))
            customer = customers[from_pos]
            
            # 移除该客户
            customers.pop(from_pos)
            
            # 随机选择插入位置
            to_pos = random.randrange(len(customers) + 1)
            
            # 插入客户
            customers.insert(to_pos, customer)
        
        # 重建染色体
        mutated_chromosome = customers + delimiters
        
        # 解码
        mutated_solution.decode(mutated_chromosome)
        
        return mutated_solution
    
    @staticmethod
    def inversion_mutation(solution, mutation_rate=0.1):
        """
        反转变异：随机选择一个子序列并反转
        
        参数:
            solution (Solution): 解决方案对象
            mutation_rate (float): 变异率
            
        返回:
            Solution: 变异后的解决方案
        """
        # 深拷贝解决方案
        mutated_solution = deepcopy(solution)
        
        # 获取客户数量
        n_customers = len(solution.customers)
        
        # 分离客户部分和车辆分隔符部分
        chromosome = mutated_solution.chromosome
        customers = [gene for gene in chromosome if gene <= n_customers]
        delimiters = [gene for gene in chromosome if gene > n_customers]
        
        # 反转变异
        if random.random() < mutation_rate and len(customers) >= 2:
            # 随机选择两个位置
            pos1, pos2 = sorted(random.sample(range(len(customers)), 2))
            
            # 反转子序列
            customers[pos1:pos2+1] = reversed(customers[pos1:pos2+1])
        
        # 重建染色体
        mutated_chromosome = customers + delimiters
        
        # 解码
        mutated_solution.decode(mutated_chromosome)
        
        return mutated_solution
    
    @staticmethod
    def scramble_mutation(solution, mutation_rate=0.1):
        """
        扰乱变异：随机选择一个子序列并随机打乱顺序
        
        参数:
            solution (Solution): 解决方案对象
            mutation_rate (float): 变异率
            
        返回:
            Solution: 变异后的解决方案
        """
        # 深拷贝解决方案
        mutated_solution = deepcopy(solution)
        
        # 获取客户数量
        n_customers = len(solution.customers)
        
        # 分离客户部分和车辆分隔符部分
        chromosome = mutated_solution.chromosome
        customers = [gene for gene in chromosome if gene <= n_customers]
        delimiters = [gene for gene in chromosome if gene > n_customers]
        
        # 扰乱变异
        if random.random() < mutation_rate and len(customers) >= 2:
            # 随机选择两个位置
            pos1, pos2 = sorted(random.sample(range(len(customers)), 2))
            
            # 提取子序列
            sub_sequence = customers[pos1:pos2+1]
            
            # 随机打乱子序列
            random.shuffle(sub_sequence)
            
            # 替换原子序列
            customers[pos1:pos2+1] = sub_sequence
        
        # 重建染色体
        mutated_chromosome = customers + delimiters
        
        # 解码
        mutated_solution.decode(mutated_chromosome)
        
        return mutated_solution
    
    @staticmethod
    def route_split_mutation(solution, mutation_rate=0.1):
        """
        路径分割变异：随机选择一条路径并将其分为两条
        
        参数:
            solution (Solution): 解决方案对象
            mutation_rate (float): 变异率
            
        返回:
            Solution: 变异后的解决方案
        """
        # 深拷贝解决方案
        mutated_solution = deepcopy(solution)
        
        # 获取客户数量
        n_customers = len(solution.customers)
        
        # 分离客户部分和车辆分隔符部分
        chromosome = mutated_solution.chromosome
        customers = [gene for gene in chromosome if gene <= n_customers]
        delimiters = [gene for gene in chromosome if gene > n_customers]
        
        # 路径分割变异
        if random.random() < mutation_rate and len(delimiters) < len(solution.vehicles) - 1:
            # 解码获取当前路径
            routes = []
            start_idx = 0
            
            for delimiter in sorted(delimiters):
                actual_delimiter = delimiter - n_customers
                routes.append(customers[start_idx:actual_delimiter])
                start_idx = actual_delimiter
            
            routes.append(customers[start_idx:])
            
            # 过滤空路径
            routes = [r for r in routes if r]
            
            if routes:
                # 随机选择一条非空路径
                route_idx = random.randrange(len(routes))
                route = routes[route_idx]
                
                if len(route) >= 2:
                    # 随机选择分割点
                    split_pos = random.randrange(1, len(route))
                    
                    # 分割路径
                    route1 = route[:split_pos]
                    route2 = route[split_pos:]
                    
                    # 更新路径
                    routes[route_idx] = route1
                    routes.insert(route_idx + 1, route2)
                    
                    # 重建客户序列和分隔符
                    new_customers = []
                    new_delimiters = []
                    current_pos = 0
                    
                    for r in routes:
                        new_customers.extend(r)
                        current_pos += len(r)
                        new_delimiters.append(current_pos + n_customers)
                    
                    # 最后一个分隔符是冗余的
                    new_delimiters.pop()
                    
                    # 更新染色体
                    mutated_chromosome = new_customers + new_delimiters
                    
                    # 解码
                    mutated_solution.decode(mutated_chromosome)
        
        return mutated_solution
    
    @staticmethod
    def route_merge_mutation(solution, mutation_rate=0.1):
        """
        路径合并变异：随机选择两条相邻路径并合并
        
        参数:
            solution (Solution): 解决方案对象
            mutation_rate (float): 变异率
            
        返回:
            Solution: 变异后的解决方案
        """
        # 深拷贝解决方案
        mutated_solution = deepcopy(solution)
        
        # 获取客户数量
        n_customers = len(solution.customers)
        
        # 分离客户部分和车辆分隔符部分
        chromosome = mutated_solution.chromosome
        customers = [gene for gene in chromosome if gene <= n_customers]
        delimiters = [gene for gene in chromosome if gene > n_customers]
        
        # 路径合并变异
        if random.random() < mutation_rate and len(delimiters) > 0:
            # 解码获取当前路径
            routes = []
            start_idx = 0
            
            for delimiter in sorted(delimiters):
                actual_delimiter = delimiter - n_customers
                routes.append(customers[start_idx:actual_delimiter])
                start_idx = actual_delimiter
            
            routes.append(customers[start_idx:])
            
            # 过滤空路径
            routes = [r for r in routes if r]
            
            if len(routes) >= 2:
                # 随机选择一个路径索引
                route_idx = random.randrange(len(routes) - 1)
                
                # 合并两条相邻路径
                merged_route = routes[route_idx] + routes[route_idx + 1]
                
                # 检查合并后路径是否超过车辆载重限制
                # 为简单起见，这里不做检查，由solution的decode方法处理可行性
                
                # 更新路径
                routes[route_idx] = merged_route
                routes.pop(route_idx + 1)
                
                # 重建客户序列和分隔符
                new_customers = []
                new_delimiters = []
                current_pos = 0
                
                for r in routes:
                    new_customers.extend(r)
                    current_pos += len(r)
                    new_delimiters.append(current_pos + n_customers)
                
                # 最后一个分隔符是冗余的
                new_delimiters.pop()
                
                # 更新染色体
                mutated_chromosome = new_customers + new_delimiters
                
                # 解码
                mutated_solution.decode(mutated_chromosome)
        
        return mutated_solution
    
    @staticmethod
    def adaptive_mutation(solution, mutation_rate=0.1, cost_params=None):
        """
        自适应变异：尝试多种变异操作，选择产生最佳后代的变异
        
        参数:
            solution (Solution): 解决方案对象
            mutation_rate (float): 变异率
            cost_params (dict): 成本参数
            
        返回:
            Solution: 变异后的解决方案
        """
        if cost_params is None:
            cost_params = {
                'fixed_cost_per_vehicle': 200,
                'distance_cost_per_km': 0.77,
                'charging_cost_per_kwh': 1.0,
                'refrigeration_cost_per_hour': 15,
                'waiting_cost_per_hour': 30
            }
        
        # 尝试所有变异操作
        mutation_methods = [
            MutationOperator.swap_mutation,
            MutationOperator.insert_mutation,
            MutationOperator.inversion_mutation,
            MutationOperator.scramble_mutation,
            MutationOperator.route_split_mutation,
            MutationOperator.route_merge_mutation
        ]
        
        # 只有以变异率概率触发变异时才执行
        if random.random() >= mutation_rate:
            return deepcopy(solution)
        
        # 尝试所有变异方法
        best_mutant = None
        best_fitness = float('inf')
        
        for mutation_method in mutation_methods:
            # 应用变异
            mutant = mutation_method(solution, 1.0)  # 确保变异发生
            
            # 评估变异体
            fitness = mutant.evaluate(cost_params)
            
            # 更新最佳变异体
            if fitness < best_fitness and mutant.is_feasible:
                best_mutant = mutant
                best_fitness = fitness
        
        # 如果没有找到可行的变异体，返回原解
        if best_mutant is None:
            return deepcopy(solution)
            
        return best_mutant 