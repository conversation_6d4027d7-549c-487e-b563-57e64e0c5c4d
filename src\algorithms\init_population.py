"""
初始种群生成模块，实现多种初始解生成策略
"""
import numpy as np
import random
import copy
from sklearn.cluster import KMeans
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.config import COST_PARAMS


class InitialPopulationGenerator:
    def __init__(self, vehicles, customers, charging_stations, distance_matrix, solution_class):
        """
        初始化种群生成器

        参数:
            vehicles (list): 车辆对象列表
            customers (list): 客户对象列表
            charging_stations (list): 充电站对象列表
            distance_matrix (array): 距离矩阵
            solution_class: 解决方案类
        """
        self.vehicles = vehicles
        self.customers = customers
        self.charging_stations = charging_stations
        self.distance_matrix = distance_matrix
        self.solution_class = solution_class

    def generate_population(self, population_size, strategy_ratios=None):
        """
        生成初始种群

        参数:
            population_size (int): 种群大小
            strategy_ratios (dict, optional): 各种生成策略的比例

        返回:
            list: 解决方案对象列表
        """
        if strategy_ratios is None:
            strategy_ratios = {
                'nearest_neighbor': 0.3,
                'savings': 0.2,
                'time_oriented': 0.2,
                'kmeans_cluster': 0.2,
                'random': 0.1
            }

        # 确保比例总和为1
        total_ratio = sum(strategy_ratios.values())
        if total_ratio != 1.0:
            for key in strategy_ratios:
                strategy_ratios[key] /= total_ratio

        # 计算每种策略生成的解决方案数量
        num_solutions = {}
        remaining = population_size

        for strategy, ratio in strategy_ratios.items():
            if strategy == list(strategy_ratios.keys())[-1]:
                # 最后一个策略，分配所有剩余
                num_solutions[strategy] = remaining
            else:
                count = int(population_size * ratio)
                num_solutions[strategy] = count
                remaining -= count

        # 生成解决方案
        population = []

        # 最近邻居策略
        for _ in range(num_solutions.get('nearest_neighbor', 0)):
            sol = self._generate_nearest_neighbor_solution()
            if sol is not None:
                population.append(sol)

        # 节约算法策略
        for _ in range(num_solutions.get('savings', 0)):
            sol = self._generate_savings_solution()
            if sol is not None:
                population.append(sol)

        # 时间导向策略
        for _ in range(num_solutions.get('time_oriented', 0)):
            sol = self._generate_time_oriented_solution()
            if sol is not None:
                population.append(sol)

        # K均值聚类策略
        for _ in range(num_solutions.get('kmeans_cluster', 0)):
            sol = self._generate_kmeans_solution()
            if sol is not None:
                population.append(sol)

        # 随机策略（补充不足）
        while len(population) < population_size:
            sol = self._generate_random_solution()
            if sol is not None:
                population.append(sol)

        return population

    def _generate_nearest_neighbor_solution(self):
        """
        使用最近邻居策略生成解决方案

        返回:
            Solution: 解决方案对象
        """
        solution = self.solution_class(self.vehicles, self.customers, self.charging_stations, self.distance_matrix)

        # 将客户随机分配给车辆
        num_vehicles = min(len(self.vehicles), len(self.customers) // 2 + 1)
        vehicle_indices = list(range(num_vehicles))
        random.shuffle(vehicle_indices)

        unassigned_customers = list(range(1, len(self.customers) + 1))  # 客户ID从1开始
        vehicle_routes = [[] for _ in range(num_vehicles)]

        # 为每辆车生成路径
        for v_idx in vehicle_indices:
            if not unassigned_customers:
                break

            # 选择一个随机的起始客户
            if unassigned_customers:
                current_customer = random.choice(unassigned_customers)
                unassigned_customers.remove(current_customer)
                vehicle_routes[v_idx].append(current_customer)

                # 使用最近邻居策略构建路径
                while unassigned_customers:
                    # 创建临时解，检查添加下一个客户是否可行
                    temp_solution = copy.deepcopy(solution)
                    temp_routes = copy.deepcopy(vehicle_routes)

                    # 找到最近的下一个客户
                    nearest_customer = None
                    nearest_distance = float('inf')

                    for customer_id in unassigned_customers:
                        distance = self.distance_matrix[current_customer][customer_id]
                        if distance < nearest_distance:
                            # 检查容量约束
                            total_demand = sum(self.customers[c-1].demand for c in temp_routes[v_idx])
                            if total_demand + self.customers[customer_id-1].demand <= self.vehicles[v_idx].max_load:
                                nearest_customer = customer_id
                                nearest_distance = distance

                    if nearest_customer is None:
                        # 当前车辆无法服务更多客户
                        break

                    # 添加最近的客户
                    current_customer = nearest_customer
                    vehicle_routes[v_idx].append(current_customer)
                    unassigned_customers.remove(current_customer)

                    # 检查是否达到车辆容量限制
                    total_demand = sum(self.customers[c-1].demand for c in vehicle_routes[v_idx])
                    if total_demand >= self.vehicles[v_idx].max_load * 0.9:  # 90%容量作为阈值
                        break

        # 编码解决方案
        solution.encode(vehicle_routes)
        solution.decode()

        # 返回解决方案前先尝试解码和评估，如果不可行返回None
        cost_params = copy.deepcopy(COST_PARAMS)

        solution.evaluate(cost_params)

        return solution if solution.is_feasible else None

    def _generate_savings_solution(self):
        """
        Clarke-Wright节约算法生成初始解

        返回:
            Solution: 解决方案对象
        """
        n_customers = len(self.customers)
        depot_id = 0

        # 创建解决方案对象
        solution = self.solution_class(
            self.vehicles,
            self.customers,
            self.charging_stations,
            self.distance_matrix
        )

        # 初始路径：每个客户单独一条路径
        routes = [[depot_id, i, depot_id] for i in range(1, n_customers + 1)]

        # 计算节约值
        savings = []
        for i in range(1, n_customers + 1):
            for j in range(i + 1, n_customers + 1):
                # 节约值公式：d(0,i) + d(0,j) - d(i,j)
                saving = (
                    self.distance_matrix[depot_id][i] +
                    self.distance_matrix[depot_id][j] -
                    self.distance_matrix[i][j]
                )

                if saving > 0:
                    savings.append((i, j, saving))

        # 按节约值降序排序
        savings.sort(key=lambda x: x[2], reverse=True)

        # 合并路径
        for i, j, _ in savings:
            # 找到包含i和j的路径
            route_i = None
            route_j = None

            for idx, route in enumerate(routes):
                if i in route:
                    route_i = (idx, route)
                if j in route:
                    route_j = (idx, route)

                if route_i and route_j:
                    break

            # 如果已经在同一路径中，跳过
            if route_i[0] == route_j[0]:
                continue

            route_i_idx, route_i_nodes = route_i
            route_j_idx, route_j_nodes = route_j

            # 确定i和j在路径中的位置
            i_pos = route_i_nodes.index(i)
            j_pos = route_j_nodes.index(j)

            # 检查i和j是否是路径的端点（不是depot）
            i_is_endpoint = (i_pos == 1 or i_pos == len(route_i_nodes) - 2)
            j_is_endpoint = (j_pos == 1 or j_pos == len(route_j_nodes) - 2)

            if not (i_is_endpoint and j_is_endpoint):
                continue

            # 获取合并后的路径
            new_route = []

            # i在路径开头
            if i_pos == 1:
                # j在路径开头
                if j_pos == 1:
                    # 反转j路径并连接
                    new_route = route_i_nodes[:-1] + route_j_nodes[::-1]
                # j在路径结尾
                else:
                    new_route = route_i_nodes[:-1] + route_j_nodes[1:]
            # i在路径结尾
            else:
                # j在路径开头
                if j_pos == 1:
                    new_route = route_i_nodes[:-1] + route_j_nodes[1:]
                # j在路径结尾
                else:
                    # 反转j路径并连接
                    new_route = route_i_nodes + route_j_nodes[-2::-1]

            # 检查约束：载重、电量等
            # 这里简化为仅检查载重约束
            route_demand = sum(self.customers[c-1].demand for c in new_route if c != depot_id)

            # 如果超过最大载重，则跳过此合并
            if route_demand > self.vehicles[0].max_load:
                continue

            # 合并路径
            routes.pop(max(route_i_idx, route_j_idx))
            routes.pop(min(route_i_idx, route_j_idx))
            routes.append(new_route)

        # 将路径转换为vehicle_routes格式
        vehicle_routes = []
        for idx, route in enumerate(routes):
            if idx < len(self.vehicles):
                vehicle_routes.append(route)
            else:
                # 如果路径数超过车辆数，尝试合并路径或丢弃
                break

        # 为未使用的车辆添加空路径
        for _ in range(len(self.vehicles) - len(vehicle_routes)):
            vehicle_routes.append([depot_id, depot_id])

        # 编码解决方案
        solution.encode(vehicle_routes)
        solution.decode()

        # 评估解决方案
        cost_params = {
            'fixed_cost_per_vehicle': 250,        # 每辆车固定成本
            'distance_cost_per_km': 2.00,         # 每公里距离成本
            'charging_cost_per_kwh': 1.04,        # 每千瓦时充电成本
            'refrigeration_cost_idle': 23.60,     # 制冷系统空载成本(每小时)
            'refrigeration_cost_loading': 38.94,  # 制冷系统装货成本(每小时)
            'waiting_cost_per_hour': 30,          # 每小时等待成本
            'time_penalty_early': 20,             # 提前到达时间惩罚(每小时)
            'time_penalty_late': 50,              # 延迟到达时间惩罚(每小时)
            'maintenance_cost_idling': 12.33,     # 空载维护成本(每小时)
            'maintenance_cost_loading': 20.74,    # 装载维护成本(每小时)
            'damage_rate_loading': 0.8,           # 装载受损率(%)
            'damage_rate_unloading': 0.6,         # 卸载受损率(%)
            'refrigeration_startup_cost': 5,      # 制冷系统启动成本
            'temp_violation_penalty': 50,         # 温度违规惩罚成本
            'multi_temp_zone_cost': 8             # 多温区额外运营成本(每小时)
        }

        solution.evaluate(cost_params)

        return solution

    def _generate_time_oriented_solution(self):
        """
        使用时间导向策略生成解决方案，考虑时间窗约束

        返回:
            Solution: 解决方案对象
        """
        solution = self.solution_class(self.vehicles, self.customers, self.charging_stations, self.distance_matrix)

        # 按照时间窗的早晚排序客户
        sorted_customers = sorted(
            range(1, len(self.customers) + 1),
            key=lambda c: self.customers[c-1].earliest_time
        )

        # 初始化车辆路径
        vehicle_routes = [[] for _ in range(len(self.vehicles))]

        # 为每个客户找到合适的车辆
        for customer_id in sorted_customers:
            customer = self.customers[customer_id-1]

            # 找到最佳车辆
            best_vehicle_idx = -1
            earliest_feasible_time = float('inf')

            for v_idx, vehicle in enumerate(self.vehicles):
                # 跳过已满载的车辆
                route_demand = sum(self.customers[c-1].demand for c in vehicle_routes[v_idx])
                if route_demand + customer.demand > vehicle.max_load:
                    continue

                # 计算到达时间
                arrival_time = 0
                current_pos = 0  # 从配送中心出发

                for c in vehicle_routes[v_idx]:
                    distance = self.distance_matrix[current_pos][c]
                    travel_time = distance / vehicle.speed * 60  # 转换为分钟
                    arrival_time += travel_time

                    # 考虑等待时间和服务时间
                    c_customer = self.customers[c-1]
                    if arrival_time < c_customer.earliest_time:
                        arrival_time = c_customer.earliest_time

                    arrival_time += c_customer.service_time
                    current_pos = c

                # 计算到新客户的时间
                distance = self.distance_matrix[current_pos][customer_id]
                travel_time = distance / vehicle.speed * 60
                new_arrival_time = arrival_time + travel_time

                # 检查时间窗约束
                if new_arrival_time <= customer.latest_time:
                    if new_arrival_time < earliest_feasible_time:
                        earliest_feasible_time = new_arrival_time
                        best_vehicle_idx = v_idx

            # 分配客户到最佳车辆
            if best_vehicle_idx >= 0:
                vehicle_routes[best_vehicle_idx].append(customer_id)
            else:
                # 如果没有合适的车辆，尝试创建新路径
                for v_idx in range(len(self.vehicles)):
                    if not vehicle_routes[v_idx]:  # 空路径
                        if self.customers[customer_id-1].demand <= self.vehicles[v_idx].max_load:
                            vehicle_routes[v_idx].append(customer_id)
                            break

        # 编码解决方案
        solution.encode(vehicle_routes)
        solution.decode()

        # 评估解决方案
        cost_params = copy.deepcopy(COST_PARAMS)

        solution.evaluate(cost_params)

        return solution if solution.is_feasible else None

    def _generate_kmeans_solution(self):
        """
        使用K-means聚类算法生成解决方案

        返回:
            Solution: 解决方案对象
        """
        solution = self.solution_class(self.vehicles, self.customers, self.charging_stations, self.distance_matrix)

        # 提取客户坐标
        customer_coords = np.array([[self.customers[i-1].x, self.customers[i-1].y] for i in range(1, len(self.customers) + 1)])

        # 确定聚类数量（基于可用车辆数和客户总需求）
        total_demand = sum(c.demand for c in self.customers)
        avg_vehicle_capacity = np.mean([v.max_load for v in self.vehicles])
        k = min(len(self.vehicles), max(1, int(np.ceil(total_demand / avg_vehicle_capacity))))

        if k <= 1 or len(self.customers) <= k:
            # 如果客户数少于或等于聚类数，或者只需要一个聚类，则使用随机策略
            return self._generate_random_solution()

        # 简单的K-means实现
        # 1. 随机选择k个中心点
        centroids = customer_coords[np.random.choice(len(customer_coords), k, replace=False)]

        # 2. 迭代直到收敛或达到最大迭代次数
        max_iter = 100
        prev_centroids = None
        clusters = None

        for _ in range(max_iter):
            # 分配点到最近的中心点
            distances = np.sqrt(((customer_coords[:, np.newaxis, :] - centroids[np.newaxis, :, :]) ** 2).sum(axis=2))
            clusters = np.argmin(distances, axis=1)

            # 更新中心点
            new_centroids = np.array([customer_coords[clusters == i].mean(axis=0) if np.sum(clusters == i) > 0 else centroids[i] for i in range(k)])

            # 检查收敛
            if prev_centroids is not None and np.all(new_centroids == prev_centroids):
                break

            prev_centroids = new_centroids
            centroids = new_centroids

        # 生成车辆路径
        vehicle_routes = [[] for _ in range(k)]

        for i, cluster_id in enumerate(clusters):
            customer_id = i + 1  # 客户ID从1开始
            vehicle_routes[cluster_id].append(customer_id)

        # 在每个簇内使用最近邻居算法进一步优化路径
        for cluster_id in range(k):
            if not vehicle_routes[cluster_id]:
                continue

            # 对每个簇内客户按照与配送中心的距离排序
            vehicle_routes[cluster_id].sort(key=lambda c: self.distance_matrix[0][c])

        # 合并路径以适应车辆数量约束
        while len([r for r in vehicle_routes if r]) > len(self.vehicles):
            # 找到最小的两个路径
            route_sizes = [(i, len(r)) for i, r in enumerate(vehicle_routes) if r]
            route_sizes.sort(key=lambda x: x[1])

            if len(route_sizes) <= 1:
                break

            # 合并最小的两个路径
            i, _ = route_sizes[0]
            j, _ = route_sizes[1]

            vehicle_routes[i].extend(vehicle_routes[j])
            vehicle_routes[j] = []

        # 移除空路径
        vehicle_routes = [r for r in vehicle_routes if r]

        # 检查每条路径是否符合车辆载重约束
        feasible_routes = []

        for i, route in enumerate(vehicle_routes):
            if i >= len(self.vehicles):
                break

            total_demand = sum(self.customers[c-1].demand for c in route)

            if total_demand <= self.vehicles[i].max_load:
                feasible_routes.append(route)
            else:
                # 如果超出载重，尝试拆分
                current_route = []
                current_demand = 0

                for c in route:
                    if current_demand + self.customers[c-1].demand <= self.vehicles[i].max_load:
                        current_route.append(c)
                        current_demand += self.customers[c-1].demand
                    else:
                        if current_route:
                            feasible_routes.append(current_route)

                        # 开始新路径
                        current_route = [c]
                        current_demand = self.customers[c-1].demand

                if current_route:
                    feasible_routes.append(current_route)

        # 编码解决方案
        solution.encode(feasible_routes)
        solution.decode()

        # 评估解决方案
        cost_params = {
            'fixed_cost_per_vehicle': 250,        # 每辆车固定成本
            'distance_cost_per_km': 2.00,         # 每公里距离成本
            'charging_cost_per_kwh': 1.04,        # 每千瓦时充电成本
            'refrigeration_cost_idle': 23.60,     # 制冷系统空载成本(每小时)
            'refrigeration_cost_loading': 38.94,  # 制冷系统装货成本(每小时)
            'waiting_cost_per_hour': 30,          # 每小时等待成本
            'time_penalty_early': 20,             # 提前到达时间惩罚(每小时)
            'time_penalty_late': 50,              # 延迟到达时间惩罚(每小时)
            'maintenance_cost_idling': 12.33,     # 空载维护成本(每小时)
            'maintenance_cost_loading': 20.74,    # 装载维护成本(每小时)
            'damage_rate_loading': 0.8,           # 装载受损率(%)
            'damage_rate_unloading': 0.6,         # 卸载受损率(%)
            'refrigeration_startup_cost': 5,      # 制冷系统启动成本
            'temp_violation_penalty': 50,         # 温度违规惩罚成本
            'multi_temp_zone_cost': 8             # 多温区额外运营成本(每小时)
        }

        solution.evaluate(cost_params)

        return solution if solution.is_feasible else None

    def _generate_random_solution(self):
        """
        生成随机解决方案

        返回:
            Solution: 解决方案对象
        """
        solution = self.solution_class(self.vehicles, self.customers, self.charging_stations, self.distance_matrix)

        # 随机打乱客户顺序
        customer_ids = list(range(1, len(self.customers) + 1))
        random.shuffle(customer_ids)

        # 随机分配客户到车辆
        num_vehicles = min(len(self.vehicles), len(self.customers) // 2 + 1)
        vehicle_routes = [[] for _ in range(num_vehicles)]

        for customer_id in customer_ids:
            # 随机选择一辆车
            v_idx = random.randrange(num_vehicles)

            # 检查载重约束
            total_demand = sum(self.customers[c-1].demand for c in vehicle_routes[v_idx])

            if total_demand + self.customers[customer_id-1].demand <= self.vehicles[v_idx].max_load:
                vehicle_routes[v_idx].append(customer_id)
            else:
                # 尝试找到其他可行的车辆
                found = False
                for alt_v_idx in range(num_vehicles):
                    if alt_v_idx == v_idx:
                        continue

                    alt_total_demand = sum(self.customers[c-1].demand for c in vehicle_routes[alt_v_idx])

                    if alt_total_demand + self.customers[customer_id-1].demand <= self.vehicles[alt_v_idx].max_load:
                        vehicle_routes[alt_v_idx].append(customer_id)
                        found = True
                        break

                # 如果所有车辆都不可行，则强制分配给原车辆
                if not found:
                    vehicle_routes[v_idx].append(customer_id)

        # 编码解决方案
        solution.encode(vehicle_routes)
        solution.decode()

        # 评估解决方案
        cost_params = copy.deepcopy(COST_PARAMS)

        solution.evaluate(cost_params)

        return solution
