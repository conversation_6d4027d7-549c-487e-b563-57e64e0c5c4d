"""
可视化模块，用于可视化路径规划结果和算法性能
"""
import matplotlib.pyplot as plt
import numpy as np
import random
import matplotlib

# 设置中文字体支持和字体大小
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']  # 优先使用的中文字体
plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像时负号'-'显示为方块的问题
plt.rcParams['font.size'] = 15  # 设置默认字体大小
plt.rcParams['axes.labelsize'] = 17  # 坐标轴标签字体大小
plt.rcParams['axes.titlesize'] = 19  # 标题字体大小
plt.rcParams['xtick.labelsize'] = 15  # x轴刻度标签字体大小
plt.rcParams['ytick.labelsize'] = 15  # y轴刻度标签字体大小
plt.rcParams['legend.fontsize'] = 15  # 图例字体大小

def plot_solution(solution, depot, customers, charging_stations, title=None, show=True, save_path=None):
    """
    可视化解决方案

    参数:
        solution (Solution): 解决方案对象
        depot (Customer): 配送中心对象
        customers (list): 客户对象列表
        charging_stations (list): 充电站对象列表
        title (str, optional): 图表标题
        show (bool): 是否显示图表
        save_path (str, optional): 保存路径
    """
    # 创建图形
    plt.figure(figsize=(12, 10))

    # 绘制配送中心
    plt.scatter(depot.x, depot.y, c='red', s=200, marker='*', label='配送中心')

    # 绘制客户点
    customer_x = [customer.x for customer in customers]
    customer_y = [customer.y for customer in customers]
    plt.scatter(customer_x, customer_y, c='blue', s=50, label='客户')

    # 添加客户编号
    for customer in customers:
        plt.annotate(f"C{customer.id}", (customer.x, customer.y),
                    xytext=(3, 3), textcoords='offset points', fontsize=12)

    # 绘制充电站
    charging_x = [station.x for station in charging_stations]
    charging_y = [station.y for station in charging_stations]
    plt.scatter(charging_x, charging_y, c='green', s=100, marker='s', label='充电站')

    # 添加充电站编号
    for station in charging_stations:
        plt.annotate(f"CS{station.id}", (station.x, station.y),
                    xytext=(3, 3), textcoords='offset points', fontsize=12)

    # 绘制路径
    colors = plt.cm.tab10(np.linspace(0, 1, 10))
    colors = np.vstack((colors, plt.cm.tab20(np.linspace(0, 1, 20))))

    # 为每条路径分配颜色
    print("\n绘制路径图时经过的节点编号:")
    total_nodes = 0
    customer_nodes = 0
    depot_nodes = 0
    charging_nodes = 0

    for i, route in enumerate(solution.vehicle_routes):
        if len(route) <= 2:  # 跳过空路径（只有depot-depot）
            continue

        route_color = colors[i % len(colors)]

        # 构建路径坐标
        route_x = []
        route_y = []

        # 打印当前路径的节点编号
        route_str = f"车辆 {i+1}: "

        for node_id in route:
            total_nodes += 1
            if node_id == 0:  # 配送中心
                route_x.append(depot.x)
                route_y.append(depot.y)
                route_str += "配送中心(0) -> "
                depot_nodes += 1
            elif 1 <= node_id <= len(customers):  # 客户
                route_x.append(customers[node_id - 1].x)
                route_y.append(customers[node_id - 1].y)
                route_str += f"C{node_id} -> "
                customer_nodes += 1
            else:  # 充电站
                station_idx = node_id - len(customers) - 1
                route_x.append(charging_stations[station_idx].x)
                route_y.append(charging_stations[station_idx].y)
                route_str += f"充电站{charging_stations[station_idx].id} -> "
                charging_nodes += 1

        # 确保路径返回配送中心（闭环）
        if route[-1] != 0:  # 如果路径最后一个节点不是配送中心
            route_x.append(depot.x)  # 添加配送中心坐标作为路径终点
            route_y.append(depot.y)
            route_str += "配送中心(0)"
            total_nodes += 1
            depot_nodes += 1
        else:
            # 移除最后的箭头
            route_str = route_str[:-4]

        print(route_str)

        # 绘制路径线条
        plt.plot(route_x, route_y, color=route_color, linewidth=1.5,
                alpha=0.7, label=f'车辆 {i+1}')

        # 绘制方向箭头
        for j in range(len(route_x) - 1):
            # 计算箭头位置（线段中点）
            arrow_x = (route_x[j] + route_x[j+1]) / 2
            arrow_y = (route_y[j] + route_y[j+1]) / 2

            # 计算箭头方向
            dx = route_x[j+1] - route_x[j]
            dy = route_y[j+1] - route_y[j]

            # 标准化方向向量
            length = np.sqrt(dx**2 + dy**2)
            if length > 0:
                dx /= length
                dy /= length

            plt.arrow(arrow_x - dx*2, arrow_y - dy*2, dx*4, dy*4,
                     head_width=1.5, head_length=2, fc=route_color, ec=route_color)

    # 添加图例
    plt.legend(fontsize=15)

    # 添加标题
    if title:
        plt.title(title, fontsize=19)
    else:
        # 确保总成本使用相同的精度（2位小数）
        total_cost = round(solution.fitness, 2)
        plt.title(f'路径规划结果 (总成本: {total_cost:.2f})', fontsize=19)

    # 添加轴标签
    plt.xlabel('X 坐标', fontsize=17)
    plt.ylabel('Y 坐标', fontsize=17)

    # 设置刻度字体大小
    plt.xticks(fontsize=15)
    plt.yticks(fontsize=15)

    # 添加网格
    plt.grid(True, linestyle='--', alpha=0.7)

    # 添加解决方案信息文本框
    # 确保所有成本项都使用相同的精度（2位小数）
    total_cost = round(solution.fitness, 2)
    fixed_cost = round(solution.fixed_cost, 2)
    distance_cost = round(solution.distance_cost, 2)
    charging_cost = round(solution.charging_cost, 2)
    refrigeration_cost = round(solution.refrigeration_cost, 2)

    info_text = (
        f"总成本: {total_cost:.2f}\n"
        f"固定成本: {fixed_cost:.2f}\n"
        f"距离成本: {distance_cost:.2f}\n"
        f"充电成本: {charging_cost:.2f}\n"
        f"制冷成本: {refrigeration_cost:.2f}\n"
    )

    # 添加时间惩罚成本项（分开显示提前到达惩罚和延误到达惩罚）
    if hasattr(solution, 'early_arrival_penalty') and solution.early_arrival_penalty > 0:
        early_arrival_penalty = round(solution.early_arrival_penalty, 2)
        info_text += f"提前到达惩罚: {early_arrival_penalty:.2f}\n"
    if hasattr(solution, 'late_arrival_penalty') and solution.late_arrival_penalty > 0:
        late_arrival_penalty = round(solution.late_arrival_penalty, 2)
        info_text += f"延误到达惩罚: {late_arrival_penalty:.2f}\n"
    if hasattr(solution, 'maintenance_cost'):
        maintenance_cost = round(solution.maintenance_cost, 2)
        info_text += f"维护成本: {maintenance_cost:.2f}\n"
    if hasattr(solution, 'damage_cost'):
        damage_cost = round(solution.damage_cost, 2)
        info_text += f"货物损坏成本: {damage_cost:.2f}\n"
    if hasattr(solution, 'temp_violation_cost'):
        temp_violation_cost = round(solution.temp_violation_cost, 2)
        info_text += f"温度违规成本: {temp_violation_cost:.2f}\n"
    if hasattr(solution, 'refr_startup_cost'):
        refr_startup_cost = round(solution.refr_startup_cost, 2)
        info_text += f"制冷启停成本: {refr_startup_cost:.2f}\n"
    if hasattr(solution, 'multi_temp_zone_cost'):
        multi_temp_zone_cost = round(solution.multi_temp_zone_cost, 2)
        info_text += f"多温区成本: {multi_temp_zone_cost:.2f}\n"

    # 添加车辆和距离信息
    info_text += (
        f"使用车辆数: {sum(1 for route in solution.vehicle_routes if len(route) > 2)}\n"
        f"总行驶距离: {sum(vehicle.total_distance for vehicle in solution.vehicles if vehicle.total_distance > 0):.2f}km"
    )

    plt.figtext(0.02, 0.02, info_text, bbox=dict(facecolor='white', alpha=0.8), fontsize=12)

    # 调整布局
    plt.tight_layout()

    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    # 显示图表
    if show:
        plt.show()
    else:
        plt.close()

    # 打印节点统计信息
    print(f"\n节点统计:")
    print(f"总节点数: {total_nodes}")
    print(f"客户节点数: {customer_nodes}")
    print(f"配送中心节点数: {depot_nodes}")
    print(f"充电站节点数: {charging_nodes}")


def plot_convergence(best_fitness_history, avg_fitness_history=None, title=None, show=True, save_path=None):
    """
    绘制收敛曲线

    参数:
        best_fitness_history (list): 最佳适应度历史
        avg_fitness_history (list, optional): 平均适应度历史 (不再使用)
        title (str, optional): 图表标题 (不再使用)
        show (bool): 是否显示图表
        save_path (str, optional): 保存路径
    """
    plt.figure(figsize=(10, 6))

    # 绘制最佳适应度曲线 - 使用浅蓝色
    plt.plot(best_fitness_history, color='#66B2FF', linewidth=2)

    # 不再绘制平均适应度曲线
    # 不再添加图例
    # 不再添加标题
    # 注意：即使传入了 avg_fitness_history 和 title 参数，也不会使用它们

    # 添加轴标签
    plt.xlabel('迭代次数', fontsize=17)
    plt.ylabel('适应度值（总成本）', fontsize=17)

    # 设置刻度字体大小
    plt.xticks(fontsize=15)
    plt.yticks(fontsize=15)

    # 添加网格
    plt.grid(True, linestyle='--', alpha=0.7)

    # 调整布局
    plt.tight_layout()

    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    # 显示图表
    if show:
        plt.show()
    else:
        plt.close()


def plot_cost_breakdown(solution, show=True, save_path=None):
    """
    绘制成本分解饼图

    参数:
        solution (Solution): 解决方案对象
        show (bool): 是否显示图表
        save_path (str, optional): 保存路径
    """
    # 确保所有成本项都使用相同的精度（2位小数）
    fixed_cost = round(solution.fixed_cost, 2)
    distance_cost = round(solution.distance_cost, 2)
    charging_cost = round(solution.charging_cost, 2)
    refrigeration_cost = round(solution.refrigeration_cost, 2)

    # 收集所有成本项
    cost_items = [
        ('固定成本', fixed_cost),
        ('距离成本', distance_cost),
        ('充电成本', charging_cost),
        ('制冷成本', refrigeration_cost)
    ]

    # 添加时间惩罚成本项（分开显示提前到达惩罚和延误到达惩罚）
    if hasattr(solution, 'early_arrival_penalty') and solution.early_arrival_penalty > 0:
        early_arrival_penalty = round(solution.early_arrival_penalty, 2)
        cost_items.append(('提前到达惩罚', early_arrival_penalty))
    else:
        early_arrival_penalty = 0

    if hasattr(solution, 'late_arrival_penalty') and solution.late_arrival_penalty > 0:
        late_arrival_penalty = round(solution.late_arrival_penalty, 2)
        cost_items.append(('延误到达惩罚', late_arrival_penalty))
    else:
        late_arrival_penalty = 0

    if hasattr(solution, 'maintenance_cost') and solution.maintenance_cost > 0:
        maintenance_cost = round(solution.maintenance_cost, 2)
        cost_items.append(('维护成本', maintenance_cost))
    else:
        maintenance_cost = 0

    if hasattr(solution, 'damage_cost') and solution.damage_cost > 0:
        damage_cost = round(solution.damage_cost, 2)
        cost_items.append(('货物损坏成本', damage_cost))
    else:
        damage_cost = 0

    if hasattr(solution, 'temp_violation_cost') and solution.temp_violation_cost > 0:
        temp_violation_cost = round(solution.temp_violation_cost, 2)
        cost_items.append(('温度违规成本', temp_violation_cost))
    else:
        temp_violation_cost = 0

    if hasattr(solution, 'refr_startup_cost') and solution.refr_startup_cost > 0:
        refr_startup_cost = round(solution.refr_startup_cost, 2)
        cost_items.append(('制冷启停成本', refr_startup_cost))
    else:
        refr_startup_cost = 0

    if hasattr(solution, 'multi_temp_zone_cost') and solution.multi_temp_zone_cost > 0:
        multi_temp_zone_cost = round(solution.multi_temp_zone_cost, 2)
        cost_items.append(('多温区成本', multi_temp_zone_cost))
    else:
        multi_temp_zone_cost = 0

    # 排序并分离标签和值
    cost_items.sort(key=lambda x: x[1], reverse=True)  # 按成本降序排列
    labels, costs = zip(*cost_items) if cost_items else ([], [])

    # 计算总成本 - 使用精确的四舍五入
    # 方法1：使用已经四舍五入的各项成本之和
    calculated_total = fixed_cost + distance_cost + charging_cost + refrigeration_cost + \
                      early_arrival_penalty + late_arrival_penalty + maintenance_cost + \
                      damage_cost + temp_violation_cost + refr_startup_cost + multi_temp_zone_cost

    # 方法2：使用solution.fitness并四舍五入
    solution_total = round(solution.fitness, 2)

    # 如果两种方法的结果相差不大，使用solution.fitness的四舍五入值
    if abs(calculated_total - solution_total) <= 0.1:
        total_cost = solution_total
    else:
        # 否则使用各项成本之和
        total_cost = calculated_total

    # 确保总成本是精确的2位小数
    total_cost = round(total_cost, 2)

    # 如果总成本为0或无穷大，显示一个错误提示
    if total_cost == 0 or total_cost == float('inf'):
        plt.figure(figsize=(10, 8))
        plt.text(0.5, 0.5, '没有有效的成本数据可供显示',
                 ha='center', va='center', fontsize=16)
        plt.axis('off')

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        if show:
            plt.show()
        else:
            plt.close()
        return

    # 计算各项成本占比
    percentages = [cost / total_cost * 100 for cost in costs]

    # 为饼图创建标签（包含金额和百分比）
    pie_labels = [f'{label}\n{cost:.2f}元 ({pct:.1f}%)'
                 for label, cost, pct in zip(labels, costs, percentages)]

    # 创建图形
    plt.figure(figsize=(12, 9))

    # 生成漂亮的颜色
    colors = plt.cm.tab20(np.linspace(0, 1, len(costs)))

    # 绘制饼图 - 主成本
    explode = [0.05 if pct > 5 else 0.02 for pct in percentages]  # 突出显示主要成本项
    patches, texts, autotexts = plt.pie(
        costs, labels=None, autopct='%1.1f%%',
        startangle=90, shadow=True, explode=explode,
        colors=colors, wedgeprops={'linewidth': 1, 'edgecolor': 'white'}
    )

    # 设置饼图文本样式
    for autotext in autotexts:
        autotext.set_fontsize(12)
        autotext.set_color('white')

    # 添加标题，包含总成本
    plt.title(f'成本分解分析 - 总成本: {total_cost:.2f}元', fontsize=19, pad=20)

    # 添加图例
    plt.legend(
        labels,
        title='成本项目',
        loc='center left',
        bbox_to_anchor=(1, 0.5),
        fontsize=15,
        title_fontsize=15
    )

    # 添加成本详情文本
    cost_details = "\n".join([f"{label}: {cost:.2f}元 ({pct:.1f}%)"
                             for label, cost, pct in zip(labels, costs, percentages)])
    cost_details = f"总成本: {total_cost:.2f}元\n\n" + cost_details

    plt.figtext(0.92, 0.15, cost_details,
                bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=1'),
                fontsize=12, ha='right')

    # 保持饼图为圆形
    plt.axis('equal')

    # 调整布局
    plt.tight_layout()

    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    # 显示图表
    if show:
        plt.show()
    else:
        plt.close()


def plot_comparison(results, metric='best_fitness', labels=None, title=None, show=True, save_path=None):
    """
    比较不同算法或参数的结果

    参数:
        results (list): 结果列表，每个元素是一个结果字典
        metric (str): 要比较的指标
        labels (list, optional): 各结果的标签
        title (str, optional): 图表标题
        show (bool): 是否显示图表
        save_path (str, optional): 保存路径
    """
    # 准备数据
    values = [result[metric] for result in results]

    if labels is None:
        labels = [f'实验 {i+1}' for i in range(len(results))]

    # 创建图形
    plt.figure(figsize=(10, 6))

    # 绘制条形图
    plt.bar(labels, values, color=plt.cm.tab10(np.linspace(0, 1, len(results))))

    # 在条形上方显示数值
    for i, value in enumerate(values):
        plt.text(i, value, f'{value:.2f}', ha='center', va='bottom', fontsize=12)

    # 添加标题
    if title:
        plt.title(title, fontsize=19)
    else:
        plt.title(f'{metric} 对比', fontsize=19)

    # 添加轴标签
    plt.xlabel('实验', fontsize=17)

    metric_labels = {
        'best_fitness': '最佳适应度（总成本）',
        'fixed_cost': '固定成本',
        'distance_cost': '距离成本',
        'charging_cost': '充电成本',
        'refrigeration_cost': '制冷成本',
        'waiting_cost': '等待成本',
        'used_vehicles': '使用车辆数',
        'total_distance': '总行驶距离',
        'execution_time': '执行时间'
    }

    plt.ylabel(metric_labels.get(metric, metric), fontsize=17)

    # 设置刻度字体大小
    plt.xticks(fontsize=15)
    plt.yticks(fontsize=15)

    # 添加网格
    plt.grid(True, linestyle='--', alpha=0.7, axis='y')

    # 调整布局
    plt.tight_layout()

    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    # 显示图表
    if show:
        plt.show()
    else:
        plt.close()