实验: baseline
日期: 2025-03-30 08:36:34
总运行时间: 1.82秒

遗传算法参数:
  population_size: 20
  max_generations: 30
  elite_size: 2
  crossover_rate: 0.8
  mutation_rate: 0.2
  selection_method: tournament
  crossover_method: ordered
  mutation_method: swap

车辆参数:
  count: 8
  max_load: 2000
  battery_capacity: 60
  consumption_rate: 0.08
  speed: 45
  partial_charging_factor: 0.8

成本参数:
  fixed_cost_per_vehicle: 200
  distance_cost_per_km: 0.77
  charging_cost_per_kwh: 1.0
  refrigeration_cost_per_hour: 15
  waiting_cost_per_hour: 30

优化结果:
  总成本: 985.20
  固定成本: 400.00
  距离成本: 408.40
  充电成本: 0.00
  制冷成本: 176.80
  等待成本: 0.00
  使用车辆数: 2
  总行驶距离: 530.39km

路径详情:
  车辆 1: 配送中心 客户22 客户29 客户19 客户16 客户23 客户26 客户30 客户18 客户9 客户14 客户4 客户20 客户1 配送中心
  车辆 2: 配送中心 客户8 客户2 客户25 客户21 客户12 客户24 客户6 客户13 客户11 客户15 客户10 客户28 客户27 客户17 客户5 客户7 客户3 配送中心
