#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
TSP求解器 - 从配送中心出发，遍历所有客户点后返回配送中心
"""

import csv
import math
import random
import matplotlib.pyplot as plt
import numpy as np
import time

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

class TSPSolver:
    def __init__(self, data_file):
        """初始化TSP求解器"""
        self.data_file = data_file
        self.nodes = []  # 存储所有节点 [id, x, y]
        self.depot_id = 0  # 配送中心ID
        self.distance_matrix = None  # 距离矩阵
        self.best_route = None  # 最佳路径
        self.best_distance = float('inf')  # 最佳距离

    def load_data(self):
        """从CSV文件加载客户数据"""
        self.nodes = []
        with open(self.data_file, 'r') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过表头
            for row in reader:
                if len(row) >= 3:  # 确保行有足够的数据
                    node_id = int(row[0])
                    x = float(row[1])
                    y = float(row[2])
                    self.nodes.append([node_id, x, y])
        print(f"加载了 {len(self.nodes)} 个节点")

    def calculate_distance_matrix(self):
        """计算所有节点之间的欧几里得距离"""
        n = len(self.nodes)
        self.distance_matrix = np.zeros((n, n))

        for i in range(n):
            for j in range(n):
                if i != j:
                    x1, y1 = self.nodes[i][1], self.nodes[i][2]
                    x2, y2 = self.nodes[j][1], self.nodes[j][2]
                    # 计算欧几里得距离
                    distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
                    self.distance_matrix[i][j] = distance

    def nearest_neighbor(self, start_node=0):
        """使用最近邻算法构建初始解

        Args:
            start_node: 起始节点索引，默认为配送中心(0)
        """
        n = len(self.nodes)
        unvisited = list(range(n))  # 所有节点
        unvisited.remove(start_node)  # 移除起始节点

        route = [start_node]  # 从指定节点开始

        while unvisited:
            current = route[-1]
            nearest = min(unvisited, key=lambda x: self.distance_matrix[current][x])
            route.append(nearest)
            unvisited.remove(nearest)

        # 如果起始节点不是配送中心，需要确保路径以配送中心开始和结束
        if start_node != 0:
            # 找到配送中心在路径中的位置
            depot_idx = route.index(0)
            # 重新排列路径，使配送中心在开头
            route = route[depot_idx:] + route[:depot_idx]

        # 确保路径以配送中心结束
        if route[-1] != 0:
            route.append(0)

        return route

    def multi_start_nearest_neighbor(self, num_starts=10):
        """多起点最近邻算法，从多个不同节点开始构建路径，选择最好的一个"""
        best_route = None
        best_distance = float('inf')

        # 从配送中心开始
        route = self.nearest_neighbor(0)
        distance = self.calculate_route_distance(route)

        if distance < best_distance:
            best_route = route
            best_distance = distance

        # 从其他随机节点开始
        start_nodes = random.sample(range(1, len(self.nodes)), min(num_starts, len(self.nodes)-1))

        for start in start_nodes:
            route = self.nearest_neighbor(start)
            distance = self.calculate_route_distance(route)

            if distance < best_distance:
                best_route = route
                best_distance = distance

        print(f"多起点最近邻算法完成，最佳距离: {best_distance:.2f}")
        return best_route

    def calculate_route_distance(self, route):
        """计算给定路径的总距离"""
        total_distance = 0
        for i in range(len(route) - 1):
            total_distance += self.distance_matrix[route[i]][route[i+1]]
        return total_distance

    def two_opt_swap(self, route, i, j):
        """执行2-opt交换操作"""
        new_route = route[:i]
        new_route.extend(reversed(route[i:j+1]))
        new_route.extend(route[j+1:])
        return new_route

    def three_opt_swap(self, route, i, j, k):
        """执行3-opt交换操作

        将路径分为三段，有8种可能的重组方式，其中2种等同于原路径，
        3种等同于2-opt操作，剩下3种是真正的3-opt操作
        """
        # 提取路径的三个部分
        A = route[:i+1]
        B = route[i+1:j+1]
        C = route[j+1:k+1]
        D = route[k+1:]

        # 生成所有可能的重组（除了原始顺序）
        # 原始顺序: A-B-C-D
        routes = [
            # 2-opt操作
            A + list(reversed(B)) + C + D,  # A-B'-C-D
            A + B + list(reversed(C)) + D,  # A-B-C'-D
            A + list(reversed(B + C)) + D,  # A-B'-C'-D

            # 真正的3-opt操作
            A + C + B + D,                  # A-C-B-D
            A + list(reversed(C)) + list(reversed(B)) + D,  # A-C'-B'-D
            A + C + list(reversed(B)) + D   # A-C-B'-D
        ]

        # 计算所有路径的距离并返回最佳路径
        best_route = route
        best_distance = self.calculate_route_distance(route)

        for r in routes:
            distance = self.calculate_route_distance(r)
            if distance < best_distance:
                best_route = r
                best_distance = distance

        return best_route, best_distance

    def two_opt(self, route, max_iterations=1000):
        """使用2-opt算法优化路径"""
        best_route = route.copy()
        best_distance = self.calculate_route_distance(route)
        improved = True
        iteration = 0

        while improved and iteration < max_iterations:
            improved = False
            iteration += 1

            for i in range(1, len(best_route) - 2):
                for j in range(i + 1, len(best_route) - 1):
                    if j - i == 1:
                        continue  # 相邻边不交换

                    new_route = self.two_opt_swap(best_route, i, j)
                    new_distance = self.calculate_route_distance(new_route)

                    if new_distance < best_distance:
                        best_route = new_route
                        best_distance = new_distance
                        improved = True
                        break

                if improved:
                    break

        print(f"2-opt优化完成，迭代次数: {iteration}")
        return best_route, best_distance

    def three_opt(self, route, max_iterations=100):
        """使用3-opt算法优化路径"""
        best_route = route.copy()
        best_distance = self.calculate_route_distance(route)
        improved = True
        iteration = 0

        while improved and iteration < max_iterations:
            improved = False
            iteration += 1

            n = len(best_route)
            for i in range(0, n - 4):
                if improved:
                    break
                for j in range(i + 1, n - 3):
                    if improved:
                        break
                    for k in range(j + 1, n - 2):
                        new_route, new_distance = self.three_opt_swap(best_route, i, j, k)

                        if new_distance < best_distance:
                            best_route = new_route
                            best_distance = new_distance
                            improved = True
                            break

        print(f"3-opt优化完成，迭代次数: {iteration}")
        return best_route, best_distance

    def simulated_annealing(self, initial_route, initial_temp=1000, cooling_rate=0.99, min_temp=1e-6, max_iterations=10000):
        """使用模拟退火算法优化路径"""
        current_route = initial_route.copy()
        best_route = initial_route.copy()
        current_distance = self.calculate_route_distance(current_route)
        best_distance = current_distance

        temperature = initial_temp
        iteration = 0
        no_improvement = 0
        max_no_improvement = 10000  # 如果连续这么多次没有改进，执行重启

        # 记录收敛过程
        convergence = []

        while temperature > min_temp and iteration < max_iterations:
            iteration += 1

            # 随机选择操作类型：2-opt或3-opt
            op_type = random.choice(['2-opt', '3-opt']) if random.random() < 0.3 else '2-opt'

            if op_type == '2-opt':
                # 随机选择两个不同的位置（不包括起点和终点）
                i = random.randint(1, len(current_route) - 2)
                j = random.randint(1, len(current_route) - 2)
                while i == j:
                    j = random.randint(1, len(current_route) - 2)

                if i > j:
                    i, j = j, i

                # 生成新解（2-opt交换）
                new_route = self.two_opt_swap(current_route, i, j)
                new_distance = self.calculate_route_distance(new_route)
            else:
                # 3-opt操作
                n = len(current_route)
                i = random.randint(0, n - 5)
                j = random.randint(i + 1, n - 4)
                k = random.randint(j + 1, n - 3)

                new_route, new_distance = self.three_opt_swap(current_route, i, j, k)

            # 计算接受概率
            delta = new_distance - current_distance
            acceptance_probability = math.exp(-delta / temperature) if delta > 0 else 1.0

            # 决定是否接受新解
            if random.random() < acceptance_probability:
                current_route = new_route
                current_distance = new_distance

                # 更新最佳解
                if current_distance < best_distance:
                    best_route = current_route.copy()
                    best_distance = current_distance
                    no_improvement = 0
                else:
                    no_improvement += 1
            else:
                no_improvement += 1

            # 如果长时间没有改进，执行重启
            if no_improvement >= max_no_improvement:
                print(f"执行重启，当前迭代: {iteration}")
                # 使用当前最佳解作为新的起点，但添加一些随机扰动
                current_route = best_route.copy()
                # 随机交换几次
                for _ in range(5):
                    i = random.randint(1, len(current_route) - 2)
                    j = random.randint(1, len(current_route) - 2)
                    if i != j:
                        current_route[i], current_route[j] = current_route[j], current_route[i]

                current_distance = self.calculate_route_distance(current_route)
                no_improvement = 0
                # 提高温度以允许更多探索
                temperature = max(temperature * 2, initial_temp / 10)

            # 降温
            temperature *= cooling_rate

            # 记录收敛过程
            if iteration % 100 == 0:
                convergence.append((iteration, best_distance))

            # 每1000次迭代输出一次进度
            if iteration % 5000 == 0:
                print(f"迭代次数: {iteration}, 温度: {temperature:.6f}, 当前最佳距离: {best_distance:.2f}")

        print(f"模拟退火优化完成，迭代次数: {iteration}, 最终温度: {temperature:.8f}")
        return best_route, best_distance, convergence

    def multi_restart_simulated_annealing(self, initial_route, num_restarts=3):
        """多次重启的模拟退火算法"""
        best_route = initial_route.copy()
        best_distance = self.calculate_route_distance(initial_route)
        best_convergence = None  # 初始化变量

        for i in range(num_restarts):
            print(f"模拟退火重启 {i+1}/{num_restarts}")

            # 对初始解进行随机扰动
            if i > 0:
                perturbed_route = best_route.copy()
                # 保持首尾为配送中心，随机打乱中间部分
                middle = perturbed_route[1:-1]
                random.shuffle(middle)
                perturbed_route[1:-1] = middle
            else:
                perturbed_route = initial_route.copy()

            # 运行模拟退火
            route, distance, convergence = self.simulated_annealing(perturbed_route)

            # 第一次迭代时总是保存收敛数据
            if i == 0 or distance < best_distance:
                best_route = route
                best_distance = distance
                best_convergence = convergence

        return best_route, best_distance, best_convergence

    def solve(self):
        """求解TSP问题"""
        print("开始求解TSP问题...")
        start_time = time.time()

        # 加载数据
        self.load_data()

        # 计算距离矩阵
        self.calculate_distance_matrix()

        # 使用多起点最近邻算法构建初始解
        print("使用多起点最近邻算法构建初始解...")
        initial_route = self.multi_start_nearest_neighbor(num_starts=20)
        initial_distance = self.calculate_route_distance(initial_route)
        print(f"多起点最近邻算法初始解距离: {initial_distance:.2f}")

        # 使用2-opt算法优化
        print("使用2-opt算法优化...")
        improved_route, improved_distance = self.two_opt(initial_route, max_iterations=2000)
        print(f"2-opt优化后距离: {improved_distance:.2f}")

        # 使用3-opt算法进一步优化
        print("使用3-opt算法优化...")
        improved_route_3opt, improved_distance_3opt = self.three_opt(improved_route, max_iterations=50)
        print(f"3-opt优化后距离: {improved_distance_3opt:.2f}")

        # 使用多重启模拟退火算法进一步优化
        print("开始多重启模拟退火优化...")
        self.best_route, self.best_distance, convergence = self.multi_restart_simulated_annealing(
            improved_route_3opt, num_restarts=1)
        print(f"模拟退火优化后距离: {self.best_distance:.2f}")

        # 计算改进百分比
        improvement = (initial_distance - self.best_distance) / initial_distance * 100
        print(f"总改进: {improvement:.2f}%")

        # 检查路径是否有效（首尾都是配送中心）
        if self.best_route[0] != 0 or self.best_route[-1] != 0:
            print("警告：路径不是从配送中心开始和结束的，正在修复...")
            if self.best_route[0] != 0:
                # 找到配送中心在路径中的位置
                depot_idx = self.best_route.index(0)
                # 重新排列路径，使配送中心在开头
                self.best_route = self.best_route[depot_idx:] + self.best_route[:depot_idx]

            # 确保路径以配送中心结束
            if self.best_route[-1] != 0:
                self.best_route.append(0)

            # 重新计算距离
            self.best_distance = self.calculate_route_distance(self.best_route)
            print(f"修复后的路径距离: {self.best_distance:.2f}")

        end_time = time.time()
        print(f"求解完成，总耗时: {end_time - start_time:.2f}秒")

        # 将节点ID转换为实际ID
        self.best_route_ids = [self.nodes[i][0] for i in self.best_route]

        # 绘制收敛曲线
        self.plot_convergence(convergence)

        return self.best_route, self.best_distance

    def plot_convergence(self, convergence):
        """绘制收敛曲线"""
        if not convergence:
            print("没有收敛数据可供绘制")
            return

        iterations, distances = zip(*convergence)

        plt.figure(figsize=(10, 6))
        plt.plot(iterations, distances, 'b-', linewidth=2)
        plt.xlabel('迭代次数', fontsize=12)
        plt.ylabel('最佳距离', fontsize=12)
        plt.title('模拟退火算法收敛过程', fontsize=14)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig("TSP_convergence.png", dpi=300)
        plt.close()

    def visualize(self):
        """可视化TSP解决方案"""
        if self.best_route is None:
            print("请先运行solve()方法")
            return

        plt.figure(figsize=(12, 10))

        # 绘制所有节点
        customer_x = [self.nodes[i][1] for i in range(1, len(self.nodes))]
        customer_y = [self.nodes[i][2] for i in range(1, len(self.nodes))]
        plt.scatter(customer_x, customer_y, c='blue', s=50, label='客户')

        # 标记配送中心
        depot_x, depot_y = self.nodes[0][1], self.nodes[0][2]
        plt.scatter(depot_x, depot_y, c='red', s=200, marker='*', label='配送中心')
        plt.annotate(f"配送中心 (0)", (depot_x, depot_y), xytext=(5, 5), textcoords='offset points', fontsize=12)

        # 为每个节点添加标签
        for i, (node_id, node_x, node_y) in enumerate(self.nodes[1:], 1):
            plt.annotate(f"{node_id}", (node_x, node_y), xytext=(5, 5), textcoords='offset points', fontsize=9)

        # 绘制路径
        for i in range(len(self.best_route) - 1):
            idx1, idx2 = self.best_route[i], self.best_route[i+1]
            # 如果是从配送中心出发或返回配送中心的路径，用红色粗线
            if idx1 == 0 or idx2 == 0:
                plt.plot([self.nodes[idx1][1], self.nodes[idx2][1]],
                         [self.nodes[idx1][2], self.nodes[idx2][2]], 'r-', linewidth=2.0, alpha=0.8)
            else:
                plt.plot([self.nodes[idx1][1], self.nodes[idx2][1]],
                         [self.nodes[idx1][2], self.nodes[idx2][2]], 'b-', linewidth=1.5, alpha=0.7)

        # 标记起点和终点
        start_idx = self.best_route[1]  # 配送中心之后的第一个节点
        end_idx = self.best_route[-2]   # 返回配送中心之前的最后一个节点
        plt.scatter(self.nodes[start_idx][1], self.nodes[start_idx][2], c='green', s=100, marker='o', label='起点')
        plt.scatter(self.nodes[end_idx][1], self.nodes[end_idx][2], c='purple', s=100, marker='s', label='终点')

        # 去掉标题，在图例中添加总距离信息
        plt.xlabel("X坐标", fontsize=14)
        plt.ylabel("Y坐标", fontsize=14)
        plt.grid(True, linestyle='--', alpha=0.7)

        # 在图例中添加总距离信息
        distance_km = self.best_distance  # 直接使用原始距离值，不进行单位转换

        # 创建自定义图例
        from matplotlib.lines import Line2D
        legend_elements = [
            Line2D([0], [0], marker='o', color='w', markerfacecolor='blue', markersize=10, label='客户'),
            Line2D([0], [0], marker='*', color='w', markerfacecolor='red', markersize=15, label='配送中心'),
            Line2D([0], [0], marker='o', color='w', markerfacecolor='green', markersize=10, label='起点'),
            Line2D([0], [0], marker='s', color='w', markerfacecolor='purple', markersize=10, label='终点'),
            Line2D([0], [0], marker='', color='w', label=f'总距离：{distance_km:.2f}km')
        ]

        plt.legend(handles=legend_elements, fontsize=12, loc='upper right')

        # 去掉下方的节点顺序信息

        plt.tight_layout()
        plt.savefig("TSP_solution.png", dpi=300, bbox_inches='tight')
        plt.show()

    def print_solution(self):
        """打印解决方案"""
        if self.best_route is None:
            print("请先运行solve()方法")
            return

        print("\n最佳路径:")
        route_str = " -> ".join([str(self.nodes[i][0]) for i in self.best_route])
        print(route_str)
        print(f"总距离: {self.best_distance:.2f}")


if __name__ == "__main__":
    # 创建TSP求解器实例
    tsp_solver = TSPSolver("data/customers.csv")

    # 求解TSP问题
    tsp_solver.solve()

    # 打印解决方案
    tsp_solver.print_solution()

    # 可视化结果
    tsp_solver.visualize()
