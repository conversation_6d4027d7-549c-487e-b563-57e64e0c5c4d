实验: baseline
日期: 2025-05-06 14:56:38
总运行时间: 221.66秒

遗传算法参数:
  population_size: 200
  max_generations: 500
  elite_size: 2
  crossover_rate: 0.8
  mutation_rate: 0.2
  selection_method: tournament
  crossover_method: ordered
  mutation_method: swap

车辆参数:
  count: 8
  max_load: 1220
  battery_capacity: 50.23
  consumption_rate: 0.4
  speed: 45
  partial_charging_factor: 0.8
  refrigeration_power: 1.21
  refr_energy_ratio: 2
  temp_control_precision: 0.9
  multi_temp_zones: False
  idle_refr_power_ratio: 0.85

成本参数:
  fixed_cost_per_vehicle: 220
  distance_cost_per_km: 0.4
  charging_cost_per_kwh: 1.5862
  refrigeration_cost_idle: 24
  refrigeration_cost_loading: 40
  waiting_cost_per_hour: 30
  time_penalty_early: 50
  time_penalty_late: 50
  maintenance_cost_idling: 12
  maintenance_cost_loading: 21
  damage_rate_loading: 0
  damage_rate_unloading: 0
  refrigeration_startup_cost: 5
  temp_violation_penalty: 10
  multi_temp_zone_cost: 8

优化结果:
  总成本: 3354.30
  固定成本: 1540.00
  距离成本: 309.17
  充电成本: 0.00
  制冷成本: 651.95
  提前到达惩罚: 67.87
  延误到达惩罚: 7.31
  使用车辆数: 7
  总行驶距离: 772.91km

路径详情:
  车辆 1: 配送中心 客户12 客户9 客户27 客户30 客户32 客户20 客户35
  车辆 2: 配送中心 客户39 客户25 客户4 客户21 客户40 客户26 客户28 配送中心
  车辆 3: 配送中心 客户23 客户13 客户49
  车辆 4: 配送中心 客户41 客户22 客户15 客户43 客户42 客户37 客户14 客户38 客户16 客户17
  车辆 5: 配送中心 客户19 客户11 客户10 客户31 客户1 客户50 客户33 客户3 客户29 客户34
  车辆 6: 配送中心 客户2 客户44 客户45 客户8 客户48 客户47 客户36
  车辆 7: 配送中心 客户24 客户6 客户5 客户18 客户7 客户46
