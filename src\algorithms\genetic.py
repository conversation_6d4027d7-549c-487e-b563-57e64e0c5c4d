"""
遗传算法主框架，整合初始种群生成、选择、交叉和变异操作，
实现部分充电策略下的电动汽车冷链物流配送路径优化
"""
import numpy as np
import random
import time
import copy
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.algorithms.init_population import InitialPopulationGenerator
from src.algorithms.selection import SelectionOperator
from src.algorithms.crossover import CrossoverOperator
from src.algorithms.mutation import MutationOperator
from src.config import COST_PARAMS


class GeneticAlgorithm:
    """
    遗传算法类，实现主要的遗传算法流程
    """

    def __init__(self, vehicles, customers, charging_stations, distance_matrix, solution_class,
                 population_size=100, max_generations=200, elite_size=5,
                 crossover_rate=0.8, mutation_rate=0.2, selection_method='tournament',
                 crossover_method='ordered', mutation_method='adaptive'):
        """
        初始化遗传算法

        参数:
            vehicles (list): 车辆对象列表
            customers (list): 客户对象列表
            charging_stations (list): 充电站对象列表
            distance_matrix (array): 距离矩阵
            solution_class: 解决方案类
            population_size (int): 种群大小
            max_generations (int): 最大迭代代数
            elite_size (int): 精英个体数量
            crossover_rate (float): 交叉概率
            mutation_rate (float): 变异概率
            selection_method (str): 选择方法
            crossover_method (str): 交叉方法
            mutation_method (str): 变异方法
        """
        self.vehicles = vehicles
        self.customers = customers
        self.charging_stations = charging_stations
        self.distance_matrix = distance_matrix
        self.solution_class = solution_class

        self.population_size = population_size
        self.max_generations = max_generations
        self.elite_size = elite_size
        self.crossover_rate = crossover_rate
        self.mutation_rate = mutation_rate

        # 选择方法
        self.selection_methods = {
            'tournament': SelectionOperator.tournament_selection,
            'roulette': SelectionOperator.roulette_wheel_selection,
            'rank': SelectionOperator.rank_selection
        }
        self.selection_method = self.selection_methods.get(selection_method, SelectionOperator.tournament_selection)

        # 交叉方法
        self.crossover_methods = {
            'ordered': CrossoverOperator.order_crossover,
            'pmx': CrossoverOperator.partially_mapped_crossover,
            'edge': CrossoverOperator.edge_recombination_crossover,
            'adaptive': CrossoverOperator.adaptive_crossover
        }
        self.crossover_method = self.crossover_methods.get(crossover_method, CrossoverOperator.order_crossover)

        # 变异方法
        self.mutation_methods = {
            'swap': MutationOperator.swap_mutation,
            'insert': MutationOperator.insert_mutation,
            'inversion': MutationOperator.inversion_mutation,
            'scramble': MutationOperator.scramble_mutation,
            'route_split': MutationOperator.route_split_mutation,
            'route_merge': MutationOperator.route_merge_mutation,
            'adaptive': MutationOperator.adaptive_mutation
        }
        self.mutation_method = self.mutation_methods.get(mutation_method, MutationOperator.adaptive_mutation)

        # 初始化种群生成器
        self.population_generator = InitialPopulationGenerator(
            vehicles=self.vehicles,
            customers=self.customers,
            charging_stations=self.charging_stations,
            distance_matrix=self.distance_matrix,
            solution_class=self.solution_class
        )

        # 用于保存运行结果
        self.best_solution = None
        self.best_fitness_history = []
        self.avg_fitness_history = []
        self.execution_time = None

        # 成本参数 - 使用config.py中的参数
        self.cost_params = copy.deepcopy(COST_PARAMS)

    def run(self, verbose=True, early_stopping=20):
        """
        运行遗传算法

        参数:
            verbose (bool): 是否输出进度信息
            early_stopping (int): 连续多少代没有改进则提前终止

        返回:
            Solution: 最佳解决方案
        """
        start_time = time.time()

        # 初始化种群
        if verbose:
            print("生成初始种群...")

        population = self.population_generator.generate_population(self.population_size)

        # 评估初始种群
        for solution in population:
            solution.evaluate(self.cost_params)

        # 记录最佳解决方案
        self.best_solution = min(population, key=lambda x: x.fitness)
        self.best_fitness_history.append(self.best_solution.fitness)

        # 记录平均适应度
        avg_fitness = sum(sol.fitness for sol in population) / len(population)
        self.avg_fitness_history.append(avg_fitness)

        if verbose:
            print(f"初始最佳解: {self.best_solution.fitness:.2f}")

        # 迭代进化
        generations_without_improvement = 0
        for generation in range(self.max_generations):
            # 精英保留
            elites = SelectionOperator.elitism_selection(population, self.elite_size)

            # 创建下一代
            next_population = elites.copy()

            # 繁殖下一代直到填满种群
            while len(next_population) < self.population_size:
                # 选择父代
                parent1 = self.selection_method(population)
                parent2 = self.selection_method(population)

                # 交叉
                if random.random() < self.crossover_rate:
                    if self.crossover_method == CrossoverOperator.adaptive_crossover:
                        offspring1, offspring2 = self.crossover_method(parent1, parent2, self.solution_class, self.cost_params)
                    else:
                        offspring1, offspring2 = self.crossover_method(parent1, parent2, self.solution_class)
                else:
                    offspring1, offspring2 = copy.deepcopy(parent1), copy.deepcopy(parent2)

                # 变异
                if self.mutation_method != MutationOperator.adaptive_mutation:
                    offspring1 = self.mutation_method(offspring1, self.mutation_rate)
                    offspring2 = self.mutation_method(offspring2, self.mutation_rate)
                else:
                    offspring1 = self.mutation_method(offspring1, self.mutation_rate, self.cost_params)
                    offspring2 = self.mutation_method(offspring2, self.mutation_rate, self.cost_params)

                # 评估后代
                offspring1.evaluate(self.cost_params)
                offspring2.evaluate(self.cost_params)

                # 添加到下一代种群
                if len(next_population) < self.population_size:
                    next_population.append(offspring1)
                if len(next_population) < self.population_size:
                    next_population.append(offspring2)

            # 更新种群
            population = next_population

            # 找到当前代最佳解
            current_best = min(population, key=lambda x: x.fitness)

            # 更新全局最佳解
            if current_best.fitness < self.best_solution.fitness and current_best.is_feasible:
                self.best_solution = copy.deepcopy(current_best)
                generations_without_improvement = 0
            else:
                generations_without_improvement += 1

            # 记录历史
            self.best_fitness_history.append(self.best_solution.fitness)

            # 记录平均适应度
            avg_fitness = sum(sol.fitness for sol in population) / len(population)
            self.avg_fitness_history.append(avg_fitness)

            # 输出进度
            if verbose and (generation + 1) % 10 == 0:
                print(f"代数 {generation + 1}/{self.max_generations}, 最佳解: {self.best_solution.fitness:.2f}, 平均解: {avg_fitness:.2f}")

            # 提前终止
            if early_stopping > 0 and generations_without_improvement >= early_stopping:
                if verbose:
                    print(f"连续 {early_stopping} 代没有改进，提前终止")
                break

        # 计算执行时间
        self.execution_time = time.time() - start_time

        if verbose:
            print(f"优化完成，总执行时间: {self.execution_time:.2f}秒")
            print(f"最佳解: {self.best_solution.fitness:.2f}")

            # 输出最佳解的详细信息
            print(f"  固定成本: {self.best_solution.fixed_cost:.2f}")
            print(f"  距离成本: {self.best_solution.distance_cost:.2f}")
            print(f"  充电成本: {self.best_solution.charging_cost:.2f}")
            print(f"  制冷成本: {self.best_solution.refrigeration_cost:.2f}")
            if hasattr(self.best_solution, 'early_arrival_penalty') and self.best_solution.early_arrival_penalty > 0:
                print(f"  提前到达惩罚: {self.best_solution.early_arrival_penalty:.2f}")
            if hasattr(self.best_solution, 'late_arrival_penalty') and self.best_solution.late_arrival_penalty > 0:
                print(f"  延误到达惩罚: {self.best_solution.late_arrival_penalty:.2f}")
            print(f"  使用车辆数: {sum(1 for route in self.best_solution.vehicle_routes if len(route) > 2)}")
            print(f"  总行驶距离: {sum(vehicle.total_distance for vehicle in self.best_solution.vehicles if vehicle.total_distance > 0):.2f}km")

        return self.best_solution

    def get_results_summary(self):
        """
        获取算法运行结果摘要

        返回:
            dict: 包含结果摘要的字典
        """
        if self.best_solution is None:
            return {"error": "算法尚未运行"}

        # 计算使用车辆数
        used_vehicles = sum(1 for route in self.best_solution.vehicle_routes if len(route) > 2)

        # 计算总行驶距离
        total_distance = sum(vehicle.total_distance for vehicle in self.best_solution.vehicles if vehicle.total_distance > 0)

        # 准备结果字典
        results = {
            "best_fitness": self.best_solution.fitness,
            "fixed_cost": self.best_solution.fixed_cost,
            "distance_cost": self.best_solution.distance_cost,
            "charging_cost": self.best_solution.charging_cost,
            "refrigeration_cost": self.best_solution.refrigeration_cost,
            "used_vehicles": used_vehicles,
            "total_distance": total_distance,
            "execution_time": self.execution_time,
            "best_fitness_history": self.best_fitness_history,
            "avg_fitness_history": self.avg_fitness_history
        }

        # 添加时间惩罚成本（如果存在）
        if hasattr(self.best_solution, 'early_arrival_penalty'):
            results["early_arrival_penalty"] = self.best_solution.early_arrival_penalty
        if hasattr(self.best_solution, 'late_arrival_penalty'):
            results["late_arrival_penalty"] = self.best_solution.late_arrival_penalty

        return results