# 电动冷藏车物流配送路径优化约束条件

## 数学模型与约束条件

### 1. 路径约束（流量平衡约束）

$$\sum_{v \in V} y_i^v = 1, \forall i \in C \quad (1)$$

$$\sum_{j \in N} x_{0j}^v = \sum_{i \in N} x_{i0}^v \leq 1, \forall v \in V \quad (2)$$

$$\sum_{j \in N} x_{ij}^v = \sum_{j \in N} x_{ji}^v = y_i^v, \forall i \in C, \forall v \in V \quad (3)$$

### 2. 载重约束

$$\sum_{i \in C} d_i \cdot y_i^v \leq Q_v, \forall v \in V \quad (4)$$

### 3. 时间窗约束

$$a_i \leq \tau_i^v \leq b_i, \forall i \in C, \forall v \in V \quad (5)$$

$$\tau_i^v + s_i + t_{ij} \leq \tau_j^v + M(1-x_{ij}^v), \forall i,j \in N, \forall v \in V \quad (6)$$

### 4. 电池约束

$$\omega_i^v - e_{ij} \cdot x_{ij}^v \geq \omega_j^v - M(1-x_{ij}^v), \forall i \in N, \forall j \in C \cup \{0\}, \forall v \in V \quad (7)$$

$$\omega_i^v + \phi_i^v \leq B_v, \forall i \in S, \forall v \in V \quad (8)$$

$$\phi_i^v \geq \alpha \cdot (B_v - \omega_i^v) \cdot z_i^v, \forall i \in S, \forall v \in V \quad (9)$$

### 5. 温度控制约束

$$\theta_j^v \leq \theta_i^v + \Delta \theta_{ij} \cdot x_{ij}^v + M(1-x_{ij}^v), \forall i,j \in N, \forall v \in V \quad (10)$$

$$\theta_{min}^i \leq \theta_i^v \leq \theta_{max}^i, \forall i \in C, \forall v \in V \quad (11)$$

### 6. 目标函数

$$Z = \min\left(\sum_{v \in V} FC_v + \sum_{v \in V}\sum_{i \in N}\sum_{j \in N} DC_{ij} \cdot x_{ij}^v + \sum_{v \in V}\sum_{i \in S} CC_i \cdot \phi_i^v + \sum_{v \in V} RC_v + \sum_{v \in V} WC_v + \sum_{v \in V} PC_v + \sum_{v \in V} MC_v\right) \quad (12)$$

## 约束条件说明

公式(1)：确保每个客户点必须且只能被一辆车服务一次。

公式(2)：保证每辆车最多只能从配送中心出发一次，且必须返回配送中心。

公式(3)：表示车辆的流量守恒约束，即车辆到达某个客户点后必须离开该客户点。

公式(4)：确保每辆车的装载重量不超过其最大载重量。

公式(5)：定义时间窗口约束，确保车辆到达客户点的时间在客户要求的时间窗口内，其中$a_i$和$b_i$分别是客户$i$的最早和最晚服务时间。

公式(6)：时间连续性约束，确保车辆从节点$i$到节点$j$的时间满足连续性，其中$s_i$是节点$i$的服务时间，$t_{ij}$是从节点$i$到节点$j$的行驶时间。

公式(7)：电池电量连续性约束，确保车辆在行驶过程中电量的合理变化，考虑路段耗电量。

公式(8)：限制车辆在充电站充电后的总电量不超过电池容量。

公式(9)：定义充电站的部分充电策略，其中$\alpha$为部分充电因子，$z_i^v$表示车辆$v$是否在充电站$i$充电。

公式(10)：描述温度演变方程，表示车厢温度随时间的变化规律，考虑路段温度变化。

公式(11)：温度限制约束，保证车厢温度始终在客户要求的温度范围内。

公式(12)：目标函数，最小化总成本，包括固定成本、距离成本、充电成本、制冷成本、等待成本、时间惩罚成本和维护成本。

## 符号说明

- $V$：车辆集合
- $C$：客户点集合
- $S$：充电站集合
- $N$：所有节点集合，$N = \{0\} \cup C \cup S$，其中$0$表示配送中心
- $x_{ij}^v$：0-1变量，表示车辆$v$是否从节点$i$到$j$
- $y_i^v$：0-1变量，表示客户点$i$是否由车辆$v$服务
- $\tau_i^v$：车辆$v$到达节点$i$的时间
- $\omega_i^v$：车辆$v$离开节点$i$时的电池电量
- $\phi_i^v$：车辆$v$在充电站$i$的充电量
- $\theta_i^v$：车辆$v$到达客户$i$时的货物温度
- $s_i$：节点$i$的服务时间
- $t_{ij}$：从节点$i$到节点$j$的行驶时间
- $a_i$：客户$i$的最早服务时间
- $b_i$：客户$i$的最晚服务时间
- $Q_v$：车辆$v$的最大载重量
- $B_v$：车辆$v$的电池容量
- $M$：足够大的正数
- $d_i$：客户$i$的需求量
- $e_{ij}$：从节点$i$到$j$的耗电量
- $\alpha$：部分充电因子
- $z_i^v$：0-1变量，表示车辆$v$是否在充电站$i$充电
- $\Delta \theta_{ij}$：从$i$到$j$的温度变化量
- $\theta_{min}^i$：客户$i$要求的最低温度
- $\theta_{max}^i$：客户$i$要求的最高温度
- $FC_v$：车辆$v$的固定成本
- $DC_{ij}$：从节点$i$到$j$的距离成本
- $CC_i$：充电站$i$的充电成本
- $RC_v$：车辆$v$的制冷成本
- $WC_v$：车辆$v$的等待成本
- $PC_v$：车辆$v$的时间惩罚成本
- $MC_v$：车辆$v$的维护成本 