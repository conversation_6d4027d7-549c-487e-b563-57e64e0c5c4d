import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib import rcParams
import os
import platform

def plot_time_window_function():
    # 设置中文字体支持
    if platform.system() == 'Windows':
        # Windows系统字体路径设置
        font_path = 'C:/Windows/Fonts/simhei.ttf'  # 使用黑体
        if os.path.exists(font_path):
            prop = fm.FontProperties(fname=font_path)
            plt.rcParams['font.family'] = prop.get_name()
        else:
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']
    else:
        # Linux/Mac系统字体设置
        plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'Hiragino Sans GB', 'STHeiti']
    
    plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像时负号'-'显示为方块的问题
    
    # 设置时间点
    a_i = 20  # 最早服务时间
    b_i = 40  # 最晚服务时间
    t = np.linspace(0, 60, 1000)  # 时间范围

    # 设置成本系数 - 增大斜率差异
    c_w = 0.6  # 等待成本系数
    c_p = 2.5  # 惩罚成本系数 - 增大惩罚成本系数

    # 计算成本函数
    cost = np.zeros_like(t)
    for i in range(len(t)):
        if t[i] < a_i:
            cost[i] = c_w * (a_i - t[i])  # 等待成本
        elif t[i] > b_i:
            cost[i] = c_p * (t[i] - b_i)  # 惩罚成本

    # 创建图形
    plt.figure(figsize=(10, 6))
    
    # 设置坐标轴范围
    plt.xlim(0, 65)  # x轴从0开始
    plt.ylim(0, 22)  # y轴从0开始
    
    # 绘制主曲线
    plt.plot(t, cost, 'b-', linewidth=2.5)
    
    # 添加时间窗口区域（确保不超过x轴）
    rect = plt.axvspan(a_i, b_i, ymin=0, ymax=0.95, alpha=0.2, color='green', label='时间窗口')
    
    # 添加竖线标记斜率变化点
    plt.axvline(x=a_i, ymin=0, ymax=0.95, color='r', linestyle='--', alpha=0.7)
    plt.axvline(x=b_i, ymin=0, ymax=0.95, color='r', linestyle='--', alpha=0.7)
    
    # 在X轴下方添加ai和bi标注
    plt.text(a_i, -1.5, '$a_i$', fontsize=16, ha='center', va='center')
    plt.text(b_i, -1.5, '$b_i$', fontsize=16, ha='center', va='center')
    
    # 添加特殊点的垂直虚线与X轴的交点小标记
    plt.plot([a_i, a_i], [-0.4, 0.4], 'k-', linewidth=1.5)
    plt.plot([b_i, b_i], [-0.4, 0.4], 'k-', linewidth=1.5)

    # 添加中文标注 - 成本曲线区域说明
    plt.text(10, 8, '等待成本\n斜率=$c_w$', fontsize=14)
    plt.text(49, 15, '惩罚成本\n斜率=$c_p$', fontsize=14)

    # 设置坐标轴标签（添加数学符号）
    plt.xlabel('到达时间 $t_{ik}$', fontsize=14, labelpad=10)
    plt.ylabel('时间窗口成本 $F(t_{ik})$', fontsize=14, labelpad=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 移除具体的数值坐标轴刻度
    plt.xticks([])
    plt.yticks([])
    
    # 设置坐标轴
    ax = plt.gca()
    
    # 设置坐标轴位置和样式
    ax.spines['left'].set_position('zero')
    ax.spines['bottom'].set_position('zero')
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    # 添加坐标轴箭头
    # 创建x轴箭头
    ax.plot((1), (0), ls="", marker=">", ms=10, color="k",
            transform=ax.get_yaxis_transform(), clip_on=False)
    # 创建y轴箭头
    ax.plot((0), (1), ls="", marker="^", ms=10, color="k",
            transform=ax.get_xaxis_transform(), clip_on=False)
    
    # 添加图例
    plt.legend(fontsize=12, loc='upper left')
    
    # 获取脚本当前目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # 设置项目根目录路径
    project_root = os.path.abspath(os.path.join(script_dir, '../..'))
    # 设置保存图像的完整路径
    save_dir = os.path.join(project_root, 'results', 'plots')
    # 确保保存目录存在
    os.makedirs(save_dir, exist_ok=True)
    save_path = os.path.join(save_dir, 'time_windows_function.png')
    
    # 保存图片
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"图表已保存至: {save_path}")
    plt.close()

if __name__ == "__main__":
    plot_time_window_function() 