# 第三章模型构建文档符号一致性问题分析与修改建议

## 一、发现的主要问题

通过对《第三章模型构建.txt》文档的详细分析，发现以下符号一致性问题：

### 1. 固定成本和可变运输成本符号不一致

- 在模型参数部分（第32-38行）定义了成本相关变量：
  - `FC_v`：车辆v的固定成本
  - `DC_{ij}`：从节点i到节点j的距离成本
- 但在配送成本构成部分（第97-106行）使用了不同的符号：
  - 固定成本使用了`F_v`和`c_1`
  - 可变运输成本使用了`c_{ij}^k`和`C_2`

### 2. 集合符号不一致

- 在模型参数部分（第22-26行）定义了集合：
  - `V`：m辆电动冷链车的集合
  - `C`：n个客户点的集合
  - `S`：k个充电站的集合
  - `N`：包含配送中心、客户点和充电站的所有节点
- 但在可变运输成本计算（第105行）使用了不同的集合符号：
  - `K`：车辆集合（应为`V`）
  - `I,J`：节点集合（应为`N`）

### 3. 决策变量定义不完整

- 在模型参数部分（第27-30行）定义的决策变量不完整：
  - 缺少了`z_i^v`的明确定义，但在第30行有提及
  - 在后续约束条件（第170行）中使用了`z_i^v`

### 4. 成本符号不一致

- 在总成本目标函数（第152-154行）中：
  - 使用了`C_1`, `C_2`, `C_3`, `C_4`等符号
  - 但在模型建立部分（第174-176行）的目标函数中使用了`FC_v`, `DC_{ij}`, `CC_i`, `RC_v`等符号

### 5. 时间单位不一致

- 在成本计算中，时间单位存在不一致：
  - `c_w`和`c_p`都是以元/h为单位
  - 但在计算时（第132行和第136行）都除以60，暗示原始时间单位是分钟

### 6. 温度违规成本符号不一致

- 在模型参数部分未定义温度违规成本`TC_v`
- 但在配送成本构成部分（第149行）使用了`T{C_v}`

## 二、修改建议

针对上述问题，我已在《模型构建（已优化）.md》文件中进行了以下修改：

### 1. 统一固定成本和可变运输成本符号

- 统一使用`FC_v`表示车辆v的固定成本
- 统一使用`DC_{ij}`表示从节点i到节点j的距离成本
- 保留`c_1`作为单位车辆固定成本

### 2. 统一集合符号

- 统一使用`V`表示车辆集合
- 统一使用`N`表示节点集合
- 在可变运输成本计算公式中，将`K`改为`V`，将`I,J`改为`N`

### 3. 完善决策变量定义

- 明确定义`z_i^v \in \{0,1\}`：若车辆v在充电站i充电，则取值1，否则为0

### 4. 统一成本符号

- 在总成本目标函数中，保留`C_1`, `C_2`, `C_3`, `C_4`作为各类成本的总和
- 在模型建立部分的目标函数中，使用具体的成本变量`FC_v`, `DC_{ij}`, `CC_i`, `RC_v`等
- 确保两种表示方式在数学上等价

### 5. 统一时间单位

- 明确标注时间单位：
  - 客户相关时间变量（如`a_i`, `b_i`, `s_i`, `t_{ij}`）单位为分钟(min)
  - 成本相关时间变量（如`c_w`, `c_p`）单位为元/小时(元/h)
  - 在计算时，通过除以60将分钟转换为小时

### 6. 添加温度违规成本定义

- 在成本相关变量部分添加`TC_v`：车辆v的温度违规成本（元）

## 三、其他优化

1. 将`p_e`的描述从"单位能耗下的制冷系统运行成本"修改为"制冷系统单位时间运行成本"，以更准确地反映其含义
2. 确保充电站的充电速率参数设定只依赖于data/charging_stations.csv文件
3. 统一使用Markdown格式，提高文档可读性
4. 添加图片引用（时间窗口成本函数图）
5. 优化公式编号和引用方式

以上修改保持了原文档的核心内容和结构，仅针对符号一致性问题进行了优化，确保模型描述的严谨性和一致性。
