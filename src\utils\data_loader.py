"""
数据加载工具，用于加载测试数据和生成距离矩阵
"""
import numpy as np
import pandas as pd
import math
import sys
import os
import copy

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.models.customer import Customer
from src.models.vehicle import Vehicle
from src.models.charging import ChargingStation
from src.config import VEHICLE_PARAMS, REFRIGERATION_CONFIG


def load_test_data(customer_file, charging_station_file, vehicle_params=None):
    """
    加载测试数据，包括客户、充电站和车辆信息
    
    参数:
        customer_file (str): 客户数据文件
        charging_station_file (str): 充电站数据文件
        vehicle_params (dict): 车辆参数
        
    返回:
        depot (Depot): 配送中心
        customers (list): 客户列表
        charging_stations (list): 充电站列表
        vehicles (list): 车辆列表
        distance_matrix (array): 距离矩阵
    """
    # 读取客户数据
    customer_df = pd.read_csv(customer_file)
    
    # 读取充电站数据
    charging_station_df = pd.read_csv(charging_station_file)
    
    # 创建客户列表（配送中心索引为0）
    customers = []
    depot = None
    
    for _, row in customer_df.iterrows():
        if row['id'] == 0:
            # 配送中心
            depot = Customer(
                id=row['id'],
                x=row['x'],
                y=row['y'],
                demand=0,
                service_time=0,
                time_window=(0, 24*60),  # 配送中心全天开放（24小时）
                temp_requirement={"zone": "冷藏", "temp": -18}
            )
        else:
            # 客户
            customer = Customer(
                id=row['id'],
                x=row['x'],
                y=row['y'],
                demand=row['demand'],
                service_time=row['service_time'],
                time_window=(row['earliest_time'], row['latest_time']),
                temp_requirement={"zone": "冷藏", "temp": -18}  # 默认需求
            )
            customers.append(customer)
    
    # 创建充电站列表
    charging_stations = []
    
    for _, row in charging_station_df.iterrows():
        charging_station = ChargingStation(
            id=row['id'],
            x=row['x'],
            y=row['y'],
            charging_rate=row['charging_rate'],
            queue_time=row.get('queue_time', 0)
        )
        charging_stations.append(charging_station)
    
    # 设置默认车辆参数，使用配置文件中的参数
    if vehicle_params is None:
        # 使用深拷贝确保不会修改原始VEHICLE_PARAMS
        vehicle_params = copy.deepcopy(VEHICLE_PARAMS)
    
    # 打印使用的参数，确保车辆参数正确
    print("\n车辆参数验证:")
    print(f"配置文件中 max_load: {VEHICLE_PARAMS['max_load']}")
    print(f"使用的 max_load: {vehicle_params['max_load']}")
    print(f"配置文件中 battery_capacity: {VEHICLE_PARAMS['battery_capacity']}")
    print(f"使用的 battery_capacity: {vehicle_params['battery_capacity']}")
    
    # 强制保证参数正确 - 如果传入的参数与配置不一致，以配置为准
    if vehicle_params['max_load'] != VEHICLE_PARAMS['max_load']:
        print(f"警告: 传入的max_load值({vehicle_params['max_load']})与配置文件不一致，已修正为{VEHICLE_PARAMS['max_load']}")
        vehicle_params['max_load'] = VEHICLE_PARAMS['max_load']
        
    if vehicle_params['battery_capacity'] != VEHICLE_PARAMS['battery_capacity']:
        print(f"警告: 传入的battery_capacity值({vehicle_params['battery_capacity']})与配置文件不一致，已修正为{VEHICLE_PARAMS['battery_capacity']}")
        vehicle_params['battery_capacity'] = VEHICLE_PARAMS['battery_capacity']
    
    # 创建车辆对象列表
    vehicles = []
    for i in range(vehicle_params['count']):
        # 构建包含制冷系统参数的车辆对象
        vehicle_args = {
            'id': i,
            'max_load': vehicle_params['max_load'],
            'battery_capacity': vehicle_params['battery_capacity'],
            'consumption_rate': vehicle_params['consumption_rate'],
            'speed': vehicle_params['speed'],
            'partial_charging_factor': vehicle_params.get('partial_charging_factor', 0.8)
        }
        
        # 添加制冷系统相关参数（如果存在）
        if 'refrigeration_power' in vehicle_params:
            vehicle_args['refrigeration_power'] = vehicle_params['refrigeration_power']
        if 'refr_energy_ratio' in vehicle_params:
            vehicle_args['refr_energy_ratio'] = vehicle_params['refr_energy_ratio']
        if 'temp_control_precision' in vehicle_params:
            vehicle_args['temp_control_precision'] = vehicle_params['temp_control_precision']
        if 'multi_temp_zones' in vehicle_params:
            vehicle_args['multi_temp_zones'] = vehicle_params['multi_temp_zones']
        if 'idle_refr_power_ratio' in vehicle_params:
            vehicle_args['idle_refr_power_ratio'] = vehicle_params['idle_refr_power_ratio']
            
        vehicle = Vehicle(**vehicle_args)
        # 初始化制冷系统状态
        vehicle.toggle_refrigeration(True)  # 默认开启制冷系统
        vehicles.append(vehicle)
    
    # 创建距离矩阵
    # 顺序：[depot, customer1, customer2, ..., charging_station1, charging_station2, ...]
    total_nodes = 1 + len(customers) + len(charging_stations)
    distance_matrix = np.zeros((total_nodes, total_nodes))
    
    # 配送中心到客户的距离
    for i, customer in enumerate(customers):
        distance_matrix[0][i+1] = calculate_distance(depot.x, depot.y, customer.x, customer.y)
        distance_matrix[i+1][0] = distance_matrix[0][i+1]
    
    # 配送中心到充电站的距离
    for i, station in enumerate(charging_stations):
        idx = 1 + len(customers) + i
        distance_matrix[0][idx] = calculate_distance(depot.x, depot.y, station.x, station.y)
        distance_matrix[idx][0] = distance_matrix[0][idx]
    
    # 客户到客户的距离
    for i, customer1 in enumerate(customers):
        for j, customer2 in enumerate(customers):
            if i != j:
                distance_matrix[i+1][j+1] = calculate_distance(customer1.x, customer1.y, customer2.x, customer2.y)
    
    # 客户到充电站的距离
    for i, customer in enumerate(customers):
        for j, station in enumerate(charging_stations):
            station_idx = 1 + len(customers) + j
            distance_matrix[i+1][station_idx] = calculate_distance(customer.x, customer.y, station.x, station.y)
            distance_matrix[station_idx][i+1] = distance_matrix[i+1][station_idx]
    
    # 充电站到充电站的距离
    for i, station1 in enumerate(charging_stations):
        for j, station2 in enumerate(charging_stations):
            if i != j:
                idx1 = 1 + len(customers) + i
                idx2 = 1 + len(customers) + j
                distance_matrix[idx1][idx2] = calculate_distance(station1.x, station1.y, station2.x, station2.y)
    
    return depot, customers, charging_stations, vehicles, distance_matrix


def calculate_distance(x1, y1, x2, y2):
    """
    计算两点之间的欧氏距离
    
    参数:
        x1, y1: 第一个点的坐标
        x2, y2: 第二个点的坐标
        
    返回:
        float: 两点之间的距离
    """
    return math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)


def generate_test_data(num_customers=30, num_charging_stations=5, area_size=100, 
                       demand_range=(10, 200), time_window_width=40, seed=None,
                       include_temp_requirements=True):
    """
    生成测试数据
    
    参数:
        num_customers (int): 客户数量
        num_charging_stations (int): 充电站数量
        area_size (int): 区域大小
        demand_range (tuple): 需求范围
        time_window_width (int): 时间窗宽度
        seed (int): 随机种子
        include_temp_requirements (bool): 是否包含温度需求
        
    返回:
        tuple: (customer_df, charging_station_df)
    """
    if seed is not None:
        np.random.seed(seed)
    
    # 生成配送中心（中心位置）
    depot_x = area_size / 2
    depot_y = area_size / 2
    
    # 使用配置文件中的温区配置
    temp_zones = [zone['name'] for zone in REFRIGERATION_CONFIG['temp_zones']]
    temp_ranges = {zone['name']: zone['temp_range'] for zone in REFRIGERATION_CONFIG['temp_zones']}
    
    # 生成客户数据
    customer_data = []
    
    # 添加配送中心（ID为0）
    customer_data.append({
        'id': 0,
        'x': depot_x,
        'y': depot_y,
        'demand': 0,
        'service_time': 0,
        'earliest_time': 0,
        'latest_time': 0,
        'temp_zone': 'NA',
        'temp_requirement': 0,
        'temp_sensitivity': 0
    })
    
    # 添加客户
    for i in range(1, num_customers + 1):
        # 随机生成坐标
        x = np.random.uniform(0, area_size)
        y = np.random.uniform(0, area_size)
        
        # 随机生成需求
        demand = np.random.randint(demand_range[0], demand_range[1])
        
        # 随机生成服务时间
        service_time = np.random.randint(10, 20)
        
        # 随机生成时间窗口 - 更紧凑的时间窗口设计
        # 将一天分为三个时段，每个客户随机分配到一个时段
        time_periods = [
            (60, 240),     # 早上 1:00-4:00
            (240, 420),    # 上午 4:00-7:00
            (420, 600)     # 下午 7:00-10:00
        ]
        
        period_idx = np.random.randint(0, len(time_periods))
        period_start, period_end = time_periods[period_idx]
        
        # 在选定的时段内，随机生成一个紧凑的时间窗口
        start_time = np.random.randint(period_start, period_end - time_window_width)
        end_time = start_time + time_window_width
        
        # 生成温度需求
        if include_temp_requirements:
            # 随机选择温区
            temp_zone = np.random.choice(temp_zones, p=[0.5, 0.3, 0.2])  # 冷冻区需求较多
            # 在温区范围内随机生成具体温度需求
            min_temp, max_temp = temp_ranges[temp_zone]
            temp_requirement = np.random.uniform(min_temp, max_temp)
            # 随机生成温度敏感度
            temp_sensitivity = np.random.uniform(0.7, 1.0)
        else:
            temp_zone = '冷冻'
            temp_requirement = -18
            temp_sensitivity = 1.0
        
        customer_data.append({
            'id': i,
            'x': x,
            'y': y,
            'demand': demand,
            'service_time': service_time,
            'earliest_time': start_time,
            'latest_time': end_time,
            'temp_zone': temp_zone,
            'temp_requirement': temp_requirement,
            'temp_sensitivity': temp_sensitivity
        })
    
    # 生成充电站数据
    charging_station_data = []
    
    for i in range(num_charging_stations):
        # 随机生成坐标
        x = np.random.uniform(0, area_size)
        y = np.random.uniform(0, area_size)
        
        # 充电速率
        charging_rate = np.random.uniform(0.5, 1.5)
        
        # 排队时间
        queue_time = np.random.randint(0, 15)
        
        charging_station_data.append({
            'id': i,
            'x': x,
            'y': y,
            'charging_rate': charging_rate,
            'queue_time': queue_time
        })
    
    # 创建DataFrame
    customer_df = pd.DataFrame(customer_data)
    charging_station_df = pd.DataFrame(charging_station_data)
    
    return customer_df, charging_station_df


def save_test_data(customer_df, charging_station_df, customer_file, charging_station_file):
    """
    保存测试数据
    
    参数:
        customer_df (DataFrame): 客户数据
        charging_station_df (DataFrame): 充电站数据
        customer_file (str): 客户数据文件路径
        charging_station_file (str): 充电站数据文件路径
    """
    customer_df.to_csv(customer_file, index=False)
    charging_station_df.to_csv(charging_station_file, index=False) 