#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VRP求解器 - 使用遗传算法求解车辆路径问题
从配送中心出发，使用7辆车遍历所有客户点，然后返回配送中心
"""

import csv
import math
import random
import matplotlib.pyplot as plt
import numpy as np
import time

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

class VRPSolver:
    def __init__(self, data_file, num_vehicles=7):
        """初始化VRP求解器"""
        self.data_file = data_file
        self.num_vehicles = num_vehicles
        self.nodes = []  # 存储所有节点 [id, x, y]
        self.depot_id = 0  # 配送中心ID
        self.distance_matrix = None  # 距离矩阵
        self.best_routes = None  # 最佳路径
        self.best_distance = float('inf')  # 最佳距离

    def load_data(self):
        """从CSV文件加载客户数据"""
        self.nodes = []
        with open(self.data_file, 'r') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过表头
            for row in reader:
                if len(row) >= 3:  # 确保行有足够的数据
                    node_id = int(row[0])
                    x = float(row[1])
                    y = float(row[2])
                    self.nodes.append([node_id, x, y])
        print(f"加载了 {len(self.nodes)} 个节点")

    def calculate_distance_matrix(self):
        """计算所有节点之间的欧几里得距离"""
        n = len(self.nodes)
        self.distance_matrix = np.zeros((n, n))

        for i in range(n):
            for j in range(n):
                if i != j:
                    x1, y1 = self.nodes[i][1], self.nodes[i][2]
                    x2, y2 = self.nodes[j][1], self.nodes[j][2]
                    # 计算欧几里得距离
                    distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
                    self.distance_matrix[i][j] = distance

    def calculate_route_distance(self, route):
        """计算单条路径的总距离"""
        total_distance = 0
        # 从配送中心到第一个客户
        if len(route) > 0:
            total_distance += self.distance_matrix[0][route[0]]

            # 计算路径上相邻客户之间的距离
            for i in range(len(route) - 1):
                total_distance += self.distance_matrix[route[i]][route[i+1]]

            # 从最后一个客户回到配送中心
            total_distance += self.distance_matrix[route[-1]][0]

        return total_distance

    def calculate_total_distance(self, routes):
        """计算所有路径的总距离"""
        return sum(self.calculate_route_distance(route) for route in routes)

    def create_initial_population(self, pop_size=100):
        """创建初始种群"""
        population = []

        for _ in range(pop_size):
            # 创建一个随机解
            solution = self.create_random_solution()
            population.append(solution)

        return population

    def create_random_solution(self):
        """创建一个随机解"""
        # 创建客户列表（不包括配送中心）
        customers = list(range(1, len(self.nodes)))
        # 随机打乱客户顺序
        random.shuffle(customers)

        # 将客户分配给车辆
        routes = [[] for _ in range(self.num_vehicles)]

        # 计算每辆车至少分配的客户数
        min_customers_per_vehicle = len(customers) // self.num_vehicles
        # 计算有多少辆车需要额外分配一个客户
        extra_customers = len(customers) % self.num_vehicles

        # 分配客户给车辆
        customer_idx = 0
        for vehicle_idx in range(self.num_vehicles):
            # 计算当前车辆应分配的客户数
            customers_for_this_vehicle = min_customers_per_vehicle
            if vehicle_idx < extra_customers:
                customers_for_this_vehicle += 1

            # 分配客户给当前车辆
            for _ in range(customers_for_this_vehicle):
                if customer_idx < len(customers):
                    routes[vehicle_idx].append(customers[customer_idx])
                    customer_idx += 1

        return routes

    def tournament_selection(self, population, fitnesses, tournament_size=3):
        """锦标赛选择"""
        selected = []

        for _ in range(len(population)):
            # 随机选择tournament_size个个体
            tournament_indices = random.sample(range(len(population)), tournament_size)
            # 选择适应度最好的个体
            best_idx = min(tournament_indices, key=lambda i: fitnesses[i])
            selected.append(population[best_idx])

        return selected

    def crossover(self, parent1, parent2):
        """交叉操作"""
        child1 = [[] for _ in range(self.num_vehicles)]
        child2 = [[] for _ in range(self.num_vehicles)]

        # 随机选择交叉点
        crossover_point = random.randint(1, self.num_vehicles - 1)

        # 前半部分直接复制
        for i in range(crossover_point):
            child1[i] = parent1[i].copy()
            child2[i] = parent2[i].copy()

        # 后半部分交叉复制
        # 获取已分配的客户
        assigned1 = set()
        assigned2 = set()

        for i in range(crossover_point):
            assigned1.update(child1[i])
            assigned2.update(child2[i])

        # 填充剩余部分
        for i in range(crossover_point, self.num_vehicles):
            # 对于child1，从parent2中获取未分配的客户
            child1[i] = [c for c in parent2[i] if c not in assigned1]
            assigned1.update(child1[i])

            # 对于child2，从parent1中获取未分配的客户
            child2[i] = [c for c in parent1[i] if c not in assigned2]
            assigned2.update(child2[i])

        # 检查是否有未分配的客户
        all_customers = set(range(1, len(self.nodes)))

        unassigned1 = all_customers - assigned1
        unassigned2 = all_customers - assigned2

        # 随机分配未分配的客户
        for c in unassigned1:
            vehicle_idx = random.randint(0, self.num_vehicles - 1)
            child1[vehicle_idx].append(c)

        for c in unassigned2:
            vehicle_idx = random.randint(0, self.num_vehicles - 1)
            child2[vehicle_idx].append(c)

        return child1, child2

    def mutation(self, solution, mutation_rate=0.1):
        """变异操作"""
        mutated_solution = [route.copy() for route in solution]

        # 对每条路径进行变异
        for i in range(len(mutated_solution)):
            # 路径内部变异（交换两个客户）
            if len(mutated_solution[i]) >= 2 and random.random() < mutation_rate:
                idx1, idx2 = random.sample(range(len(mutated_solution[i])), 2)
                mutated_solution[i][idx1], mutated_solution[i][idx2] = mutated_solution[i][idx2], mutated_solution[i][idx1]

            # 路径之间变异（将一个客户从一条路径移动到另一条路径）
            if random.random() < mutation_rate:
                if len(mutated_solution[i]) > 0:  # 确保当前路径有客户
                    # 随机选择一个客户
                    customer_idx = random.randint(0, len(mutated_solution[i]) - 1)
                    customer = mutated_solution[i].pop(customer_idx)

                    # 随机选择另一条路径
                    other_route_idx = random.randint(0, self.num_vehicles - 1)
                    while other_route_idx == i:
                        other_route_idx = random.randint(0, self.num_vehicles - 1)

                    # 将客户添加到另一条路径
                    insert_pos = random.randint(0, len(mutated_solution[other_route_idx]))
                    mutated_solution[other_route_idx].insert(insert_pos, customer)

        return mutated_solution

    def optimize_routes(self, routes):
        """优化每条路径（使用2-opt算法）"""
        optimized_routes = []

        for route in routes:
            if len(route) <= 2:
                optimized_routes.append(route.copy())
                continue

            # 应用2-opt算法
            best_route = route.copy()
            best_distance = self.calculate_route_distance(best_route)
            improved = True

            while improved:
                improved = False

                for i in range(len(best_route) - 1):
                    for j in range(i + 2, len(best_route)):
                        # 尝试2-opt交换
                        new_route = best_route.copy()
                        new_route[i+1:j+1] = reversed(best_route[i+1:j+1])

                        new_distance = self.calculate_route_distance(new_route)

                        if new_distance < best_distance:
                            best_route = new_route
                            best_distance = new_distance
                            improved = True
                            break

                    if improved:
                        break

            optimized_routes.append(best_route)

        return optimized_routes

    def genetic_algorithm(self, pop_size=100, max_generations=500, crossover_rate=0.8, mutation_rate=0.1):
        """使用遗传算法求解VRP"""
        # 创建初始种群
        population = self.create_initial_population(pop_size)

        # 记录最佳解
        best_solution = None
        best_fitness = float('inf')

        # 记录收敛过程
        convergence = []

        # 开始迭代
        for generation in range(max_generations):
            # 计算适应度
            fitnesses = [self.calculate_total_distance(solution) for solution in population]

            # 更新最佳解
            min_fitness_idx = fitnesses.index(min(fitnesses))
            if fitnesses[min_fitness_idx] < best_fitness:
                best_solution = [route.copy() for route in population[min_fitness_idx]]
                best_fitness = fitnesses[min_fitness_idx]

                # 优化最佳解的路径
                best_solution = self.optimize_routes(best_solution)
                best_fitness = self.calculate_total_distance(best_solution)

            # 记录收敛过程
            convergence.append(best_fitness)

            # 每10代输出一次进度
            if generation % 10 == 0:
                print(f"迭代次数 {generation}: 最佳距离 = {best_fitness:.2f}")

            # 选择
            selected = self.tournament_selection(population, fitnesses)

            # 创建新一代
            new_population = []

            # 精英保留
            elite_count = max(1, int(pop_size * 0.1))
            sorted_indices = sorted(range(len(fitnesses)), key=lambda i: fitnesses[i])
            for i in range(elite_count):
                new_population.append(population[sorted_indices[i]])

            # 交叉和变异
            while len(new_population) < pop_size:
                # 随机选择两个父代
                parent1 = random.choice(selected)
                parent2 = random.choice(selected)

                # 交叉
                if random.random() < crossover_rate:
                    child1, child2 = self.crossover(parent1, parent2)
                else:
                    child1, child2 = parent1.copy(), parent2.copy()

                # 变异
                child1 = self.mutation(child1, mutation_rate)
                child2 = self.mutation(child2, mutation_rate)

                # 添加到新种群
                new_population.append(child1)
                if len(new_population) < pop_size:
                    new_population.append(child2)

            # 更新种群
            population = new_population

        # 返回最佳解
        self.best_routes = best_solution
        self.best_distance = best_fitness

        return best_solution, best_fitness, convergence

    def solve(self):
        """求解VRP问题"""
        print("开始求解VRP问题...")
        start_time = time.time()

        # 加载数据
        self.load_data()

        # 计算距离矩阵
        self.calculate_distance_matrix()

        # 使用遗传算法求解
        print("使用遗传算法求解VRP...")
        self.best_routes, self.best_distance, convergence = self.genetic_algorithm(
            pop_size=100,
            max_generations=500,
            crossover_rate=0.8,
            mutation_rate=0.1
        )

        end_time = time.time()
        print(f"求解完成，耗时: {end_time - start_time:.2f} 秒")

        # 绘制收敛曲线
        self.plot_convergence(convergence)

        return self.best_routes, self.best_distance

    def plot_convergence(self, convergence):
        """绘制收敛曲线"""
        plt.figure(figsize=(10, 6))
        plt.plot(convergence, 'b-', linewidth=2)
        plt.xlabel('迭代次数', fontsize=12)
        plt.ylabel('最佳距离', fontsize=12)
        plt.title('遗传算法收敛过程', fontsize=14)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig("VRP_convergence.png", dpi=300)
        plt.close()

    def visualize(self):
        """可视化VRP解决方案"""
        if self.best_routes is None:
            print("请先运行solve()方法")
            return

        plt.figure(figsize=(12, 10))

        # 绘制所有节点
        customer_x = [self.nodes[i][1] for i in range(1, len(self.nodes))]
        customer_y = [self.nodes[i][2] for i in range(1, len(self.nodes))]
        plt.scatter(customer_x, customer_y, c='#4682B4', s=60, edgecolors='black', linewidths=0.5, alpha=0.8, label='客户')

        # 标记配送中心
        depot_x, depot_y = self.nodes[0][1], self.nodes[0][2]
        plt.scatter(depot_x, depot_y, c='red', s=250, marker='*', edgecolors='black', linewidths=0.5, label='配送中心')
        plt.annotate(f"0", (depot_x, depot_y), xytext=(5, 5), textcoords='offset points', fontsize=12, fontweight='bold')

        # 为每个节点添加标签
        for i, (node_id, node_x, node_y) in enumerate(self.nodes[1:], 1):
            plt.annotate(f"{node_id}", (node_x, node_y), xytext=(5, 5), textcoords='offset points', fontsize=9, color='black')

        # 绘制路径 - 使用更浅的颜色
        colors = ['#90EE90', '#D8BFD8', '#FFD700', '#F4A460', '#FFB6C1', '#C0C0C0', '#CAFF70', '#AFEEEE', '#EE82EE', '#98FB98']

        for vehicle_idx, route in enumerate(self.best_routes):
            if not route:  # 如果路径为空，跳过
                continue

            color = colors[vehicle_idx % len(colors)]

            # 从配送中心到第一个客户
            plt.plot([depot_x, self.nodes[route[0]][1]],
                     [depot_y, self.nodes[route[0]][2]],
                     c=color, linestyle='-', linewidth=2.5, alpha=0.8,
                     label=f"车辆 {vehicle_idx+1}: {len(route)} 个客户")

            # 绘制路径上相邻客户之间的连线
            for i in range(len(route) - 1):
                plt.plot([self.nodes[route[i]][1], self.nodes[route[i+1]][1]],
                         [self.nodes[route[i]][2], self.nodes[route[i+1]][2]],
                         c=color, linestyle='-', linewidth=2.5, alpha=0.8)

            # 从最后一个客户回到配送中心
            plt.plot([self.nodes[route[-1]][1], depot_x],
                     [self.nodes[route[-1]][2], depot_y],
                     c=color, linestyle='-', linewidth=2.5, alpha=0.8)

        plt.xlabel("X坐标", fontsize=14)
        plt.ylabel("Y坐标", fontsize=14)
        plt.grid(True, linestyle='--', alpha=0.7)

        # 在图例中添加总距离信息
        from matplotlib.lines import Line2D
        legend_elements = [
            Line2D([0], [0], marker='o', color='w', markerfacecolor='#4682B4', markersize=10, markeredgecolor='black', markeredgewidth=0.5, label='客户'),
            Line2D([0], [0], marker='*', color='w', markerfacecolor='red', markersize=15, markeredgecolor='black', markeredgewidth=0.5, label='配送中心'),
            Line2D([0], [0], marker='', color='w', label=f'总距离: {self.best_distance:.2f}')
        ]

        # 添加每辆车的图例
        for vehicle_idx, route in enumerate(self.best_routes):
            if route:  # 如果路径非空
                color = colors[vehicle_idx % len(colors)]
                legend_elements.append(
                    Line2D([0], [0], color=color, lw=2.5, alpha=0.8, label=f'车辆 {vehicle_idx+1}: {len(route)} 个客户')
                )

        plt.legend(handles=legend_elements, fontsize=12, loc='upper right')

        plt.tight_layout()
        plt.savefig("VRP_solution.png", dpi=300, bbox_inches='tight')
        plt.show()

    def print_solution(self):
        """打印解决方案"""
        if self.best_routes is None:
            print("请先运行solve()方法")
            return

        print("\n最优解:")
        for i, route in enumerate(self.best_routes):
            if route:
                print(f"车辆 {i+1}: 0 -> {' -> '.join(map(str, route))} -> 0")
            else:
                print(f"车辆 {i+1}: 0 -> 0")

        print(f"\n总距离: {self.best_distance:.2f}")

        # 检查是否所有客户都被访问
        all_customers = set()
        for route in self.best_routes:
            all_customers.update(route)

        print(f"已访问客户数量: {len(all_customers)} / {len(self.nodes) - 1}")

        if len(all_customers) < len(self.nodes) - 1:
            print("警告: 并非所有客户都被访问!")
            missing = set(range(1, len(self.nodes))) - all_customers
            print(f"未访问客户: {missing}")


if __name__ == "__main__":
    # 创建VRP求解器实例
    vrp_solver = VRPSolver("data/customers.csv", num_vehicles=7)

    # 求解VRP问题
    vrp_solver.solve()

    # 打印解决方案
    vrp_solver.print_solution()

    # 可视化结果
    vrp_solver.visualize()
