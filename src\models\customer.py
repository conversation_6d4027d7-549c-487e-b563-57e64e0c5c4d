"""
客户模型，包含客户位置、需求和时间窗约束
"""

class Customer:
    def __init__(self, id, x, y, demand, service_time, time_window, temp_requirement=None, temp_sensitivity=1.0):
        """
        初始化客户模型
        
        参数:
            id (int): 客户ID
            x (float): x坐标
            y (float): y坐标
            demand (float): 需求量(kg)
            service_time (float): 服务时间(min)
            time_window (tuple): 时间窗(min)，格式为(earliest, latest)
            temp_requirement (dict, optional): 温度需求，格式为{'temp': -18, 'zone': '冷冻'}
            temp_sensitivity (float): 温度敏感度(0-1之间)，值越高对温度波动越敏感
        """
        self.id = id
        self.x = x
        self.y = y
        self.demand = demand
        self.service_time = service_time
        self.earliest_time = time_window[0]  # 最早服务时间
        self.latest_time = time_window[1]    # 最晚服务时间
        self.time_window = time_window       # 保存完整时间窗口
        
        # 温度需求相关
        self.temp_requirement = temp_requirement if temp_requirement else {'temp': -18, 'zone': '冷冻'}
        self.temp_sensitivity = temp_sensitivity  # 温度敏感度，影响温度违规的惩罚程度
        
    def is_in_time_window(self, arrival_time):
        """
        检查到达时间是否在时间窗内
        
        参数:
            arrival_time (float): 到达时间(min)
            
        返回:
            bool: 是否在时间窗内
        """
        return self.earliest_time <= arrival_time <= self.latest_time
    
    def calculate_waiting_time(self, arrival_time):
        """
        计算等待时间
        
        参数:
            arrival_time (float): 到达时间(min)
            
        返回:
            float: 等待时间(min)
        """
        if arrival_time < self.earliest_time:
            return self.earliest_time - arrival_time
        return 0
    
    def calculate_temp_violation(self, actual_temp):
        """
        计算温度违规程度
        
        参数:
            actual_temp (float): 实际温度(°C)
            
        返回:
            float: 温度违规程度(0-1之间)，0表示无违规
        """
        required_temp = self.temp_requirement['temp']
        # 简单实现：温度差异越大，违规程度越高
        temp_diff = abs(actual_temp - required_temp)
        
        # 根据温区类型确定允许的温度波动范围
        if self.temp_requirement['zone'] == '冷冻':
            allowed_diff = 2.0  # 冷冻区允许±2°C波动
        elif self.temp_requirement['zone'] == '冷藏':
            allowed_diff = 3.0  # 冷藏区允许±3°C波动
        else:  # 恒温区
            allowed_diff = 5.0  # 恒温区允许±5°C波动
        
        if temp_diff <= allowed_diff:
            return 0
        else:
            # 计算超出允许范围的比例，考虑敏感度
            violation_degree = (temp_diff - allowed_diff) / 10.0 * self.temp_sensitivity
            return min(1.0, max(0.0, violation_degree))  # 确保在0-1范围内
    
    def __str__(self):
        """字符串表示"""
        return f"Customer {self.id}: Pos=({self.x}, {self.y}), Demand={self.demand}, TW=({self.earliest_time}, {self.latest_time}), Temp={self.temp_requirement['temp']}°C" 