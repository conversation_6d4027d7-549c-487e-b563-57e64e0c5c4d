"""
配置文件，存储算法参数和实验配置
"""

# 遗传算法参数
GA_CONFIG = {
    'population_size': 200,        # 种群大小 (简化版，原为100)
    'max_generations': 500,        # 最大迭代代数 (简化版，原为200)
    'elite_size': 2,              # 精英个体数量
    'crossover_rate': 0.8,        # 交叉概率
    'mutation_rate': 0.2,         # 变异概率
    'selection_method': 'tournament',  # 选择方法：'tournament', 'roulette', 'rank'
    'crossover_method': 'ordered',    # 交叉方法：'ordered', 'pmx', 'edge', 'adaptive'
    'mutation_method': 'swap'      # 变异方法：'swap', 'insert', 'inversion', 'scramble', 'route_split', 'route_merge', 'adaptive'
}

# 成本参数
COST_PARAMS = {
    'fixed_cost_per_vehicle': 220,        # 每辆车固定成本@
    'distance_cost_per_km': 0.4,          # 每公里距离成本@
    'charging_cost_per_kwh': 1.5862,      # 每千瓦时充电成本@
    'refrigeration_cost_idle': 24,        # 制冷系统空载成本(每小时)@
    'refrigeration_cost_loading': 40,    # 制冷系统装货成本(每小时)@
    'waiting_cost_per_hour': 30,          # 每小时等待成本@
    'time_penalty_early': 30,              # 提前到达时间惩罚(每小时)@
    'time_penalty_late': 100,             # 延迟到达时间惩罚(每小时)@
    'maintenance_cost_idling': 12,     # 空载维护成本(每小时)@
    'maintenance_cost_loading': 21,    # 装载维护成本(每小时)@
    'damage_rate_loading': 0,           # 装载受损率(%)@
    'damage_rate_unloading': 0,         # 卸载受损率(%)@
    'refrigeration_startup_cost': 5,      # 制冷系统启动成本
    'temp_violation_penalty': 10,         # 温度违规惩罚成本
    'multi_temp_zone_cost': 8             # 多温区额外运营成本(每小时)
}

# 车辆参数 - 添加制冷相关参数
VEHICLE_PARAMS = {
    'count': 10,                     # 车辆数量
    'max_load': 1220,               # 最大载重(kg)@
    'battery_capacity': 48,      # 电池容量(kWh) @- 增大电池容量以确保能找到可行解
    'consumption_rate': 0.4,     # 单位距离耗电量(kWh/km)@ - 略微降低消耗率（原论文参数为0.42）
    'speed': 45,                    # 行驶速度(km/h)@
    'partial_charging_factor': 0.8, # 部分充电因子(0-1之间)@
    'refrigeration_power': 1.21,     # 制冷设备功率(kW)@
    'refr_energy_ratio': 2,       # 制冷系统能效比@
    'temp_control_precision': 0.9,  # 温度控制精度(0-1之间)
    'multi_temp_zones': False,      # 是否支持多温区
    'idle_refr_power_ratio': 0.85    # 停车时制冷功率比例@(相对于行驶时)
}

# 制冷温区配置
REFRIGERATION_CONFIG = {
    'default_temp': -18,            # 默认温度需求(°C)
    'temp_zones': [                 # 温区定义
        {'name': '冷冻', 'temp_range': (-25, -18), 'power_factor': 1.2},
        {'name': '冷藏', 'temp_range': (-5, 5), 'power_factor': 1.0},
        {'name': '恒温', 'temp_range': (15, 25), 'power_factor': 0.8}
    ],
    'ambient_temp': 30,             # 环境温度(°C)
    'temp_rise_rate': 0.5,          # 停车时箱内温度上升速率(°C/min)
    'recovery_power_factor': 1.5    # 温度恢复需要的额外功率因子
}

# 实验参数
EXPERIMENT_CONFIG = {
    'runs': 5,                      # 实验运行次数
    'random_seed': 42,              # 随机种子
    'early_stopping': 30,           # 提前终止参数（连续多少代没有改进）
    'output_dir': 'results',        # 结果输出目录
}

# 文件路径
FILE_PATHS = {
    'customer_data': 'data/customers.csv',
    'charging_station_data': 'data/charging_stations.csv',
    'results_dir': 'results',
    'plots_dir': 'results/plots',
    'logs_dir': 'results/logs'
}