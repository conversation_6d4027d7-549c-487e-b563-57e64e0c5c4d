"""
可视化模块，用于生成路径图和电量变化曲线图
"""
import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import matplotlib

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False


class Visualizer:
    def __init__(self, solution, depot, customers, charging_stations, vehicles, output_dir=None):
        """
        初始化可视化器

        参数:
            solution (Solution): 解决方案对象
            depot (Customer): 配送中心对象
            customers (list): 客户对象列表
            charging_stations (list): 充电站对象列表
            vehicles (list): 车辆对象列表
            output_dir (str, optional): 输出目录
        """
        self.solution = solution
        self.depot = depot
        self.customers = customers
        self.charging_stations = charging_stations
        self.vehicles = vehicles

        # 添加距离矩阵引用
        self.distance_matrix = solution.distance_matrix if hasattr(solution, 'distance_matrix') else None

        # 创建默认输出目录（如果未提供）
        if output_dir is None:
            self.timestamp = datetime.now().strftime("%Y/%m/%d/%H:%M:%S")
            self.output_dir = os.path.join("analysis/outputs",
                                         self.timestamp.replace(':', '_'))
            os.makedirs(self.output_dir, exist_ok=True)
        else:
            self.output_dir = output_dir

    def plot_routes(self, show=False):
        """绘制车辆配送路线图

        参数:
            show (bool): 是否显示图表

        返回:
            str: 保存路径
        """
        # 获取路线节点坐标
        active_routes = []
        colors = ['b', 'g', 'r', 'c', 'm', 'y', 'k', 'purple', 'orange', 'brown', 'pink', 'gray', 'olive', 'cyan']

        # 创建一个大图
        plt.figure(figsize=(14, 12))

        # 绘制配送中心
        plt.plot(self.depot.x, self.depot.y, 'k*', markersize=15, label='配送中心')

        # 绘制客户点
        customer_xs = [customer.x for customer in self.customers]
        customer_ys = [customer.y for customer in self.customers]
        plt.plot(customer_xs, customer_ys, 'bo', markersize=7, label='客户点')

        # 添加客户编号标签
        for i, customer in enumerate(self.customers):
            plt.text(customer.x + 0.2, customer.y + 0.2, f'C{i+1}', fontsize=9)

        # 绘制充电站
        charging_xs = [station.x for station in self.charging_stations]
        charging_ys = [station.y for station in self.charging_stations]
        plt.plot(charging_xs, charging_ys, 'rs', markersize=10, label='充电站')

        # 添加充电站标签，清晰标注充电站ID
        for station in self.charging_stations:
            plt.text(station.x, station.y + 0.5, f'CS{int(station.id)}', fontsize=10,
                    color='red', fontweight='bold',
                    bbox=dict(facecolor='white', alpha=0.7, edgecolor='red', boxstyle='round,pad=0.3'))

        # 绘制每个车辆的路线
        for vehicle_idx, route in enumerate(self.solution.vehicle_routes):
            if len(route) <= 2:  # 跳过没有客户的路线
                continue

            # 确保路径起点是配送中心
            if route[0] != 0:
                print(f"路线 {vehicle_idx+1} 不是从配送中心出发，将添加配送中心作为起点")
                route.insert(0, 0)

            # 确保路径终点是配送中心
            if route[-1] != 0:
                print(f"路线 {vehicle_idx+1} 不是返回配送中心，将添加配送中心作为终点")
                route.append(0)

            route_x = []
            route_y = []

            # 获取路线坐标
            for node in route:
                if node == 0:  # 配送中心
                    route_x.append(self.depot.x)
                    route_y.append(self.depot.y)
                elif 1 <= node <= len(self.customers):  # 客户
                    customer = self.customers[node-1]
                    route_x.append(customer.x)
                    route_y.append(customer.y)
                else:  # 充电站
                    station_idx = node - len(self.customers) - 1
                    if station_idx < len(self.charging_stations):
                        station = self.charging_stations[station_idx]
                        route_x.append(station.x)
                        route_y.append(station.y)

            # 绘制路线
            color_idx = vehicle_idx % len(colors)
            plt.plot(route_x, route_y, color=colors[color_idx], linestyle='-', linewidth=1.5,
                    label=f'车辆 {vehicle_idx+1}', alpha=0.7)

            # 用箭头标记方向
            for i in range(len(route_x) - 1):
                # 计算角度
                dx = route_x[i+1] - route_x[i]
                dy = route_y[i+1] - route_y[i]

                # 每隔一段距离绘制一个箭头
                dist = np.sqrt(dx**2 + dy**2)
                if dist > 2.0:  # 只有在节点间距离较大时才绘制箭头
                    # 在路线的中间位置添加箭头
                    mid_x = route_x[i] + dx * 0.6
                    mid_y = route_y[i] + dy * 0.6
                    plt.arrow(mid_x - dx * 0.1, mid_y - dy * 0.1, dx * 0.2, dy * 0.2,
                             head_width=0.6, head_length=0.8, fc=colors[color_idx], ec=colors[color_idx])

            active_routes.append((vehicle_idx + 1, route))

        # 添加图例
        plt.legend(loc='upper right')

        # 添加标题
        plt.title('车辆配送路线图')

        # 添加网格
        plt.grid(True, linestyle='--', alpha=0.7)

        # 调整布局
        plt.tight_layout()

        # 保存图表
        save_path = os.path.join(self.output_dir, "route_map.png")
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

        # 显示图表
        if show:
            plt.show()
        else:
            plt.close()

        # 打印路线信息，确保与路径图展示一致
        print("\n绘制路径图时经过的节点编号:")
        for vehicle_idx, route_tuple in active_routes:
            route_str = f"车辆 {vehicle_idx}: "

            # 遍历路径中的所有节点
            for node_id in route_tuple:
                if node_id == 0:  # 配送中心
                    route_str += "配送中心(0) -> "
                elif 1 <= node_id <= len(self.customers):  # 客户
                    route_str += f"C{node_id} -> "
                else:  # 充电站
                    station_idx = node_id - len(self.customers) - 1
                    if station_idx < len(self.charging_stations):
                        route_str += f"CS{int(self.charging_stations[station_idx].id)} -> "

            # 移除最后的箭头
            route_str = route_str[:-4]
            print(route_str)

        return save_path

    def _generate_battery_timeline(self, vehicle, route):
        """
        生成车辆电量随时间变化的时间线数据

        参数:
            vehicle (Vehicle): 车辆对象
            route (list): 车辆路径

        返回:
            dict: 包含时间和电量数据的字典
        """
        # 初始化时间线数据
        timeline = {
            'time': [],           # 时间点（分钟）
            'battery_level': [],  # 电池电量（kWh）
            'node_times': [],     # 节点到达时间
            'node_labels': [],    # 节点标签
            'charging_times': [], # 充电站到达时间
            'charging_labels': [] # 充电站标签
        }

        # 如果车辆已经有电量时间线数据，直接返回
        if hasattr(vehicle, 'battery_timeline') and isinstance(vehicle.battery_timeline, dict) and 'time' in vehicle.battery_timeline and 'battery_level' in vehicle.battery_timeline:
            return vehicle.battery_timeline

        # 初始化状态
        current_time = 0.0  # 分钟
        battery_capacity = getattr(vehicle, 'battery_capacity', 40.0)
        current_battery = battery_capacity  # 初始满电

        # 添加起始点（配送中心）
        timeline['time'].append(current_time)
        timeline['battery_level'].append(current_battery)
        timeline['node_times'].append(current_time)
        timeline['node_labels'].append("配送中心")

        # 如果有详细的调度信息，使用它来构建时间线
        if hasattr(vehicle, 'schedule') and len(vehicle.schedule) > 0:
            # 遍历调度中的每个节点
            for i in range(1, len(vehicle.schedule)):
                prev_node = vehicle.schedule[i-1]
                curr_node = vehicle.schedule[i]
                node_id = curr_node['node_id']

                # 1. 行驶阶段
                travel_time = curr_node['arrival_time'] - prev_node['departure_time']
                if travel_time > 0:
                    # 获取行驶距离
                    from_node = route[i-1]
                    to_node = route[i]

                    if self.distance_matrix is not None and from_node < len(self.distance_matrix) and to_node < len(self.distance_matrix):
                        distance = self.distance_matrix[from_node][to_node]
                    else:
                        # 如果无法从距离矩阵获取，则基于时间和速度估算
                        distance = (travel_time / 60.0) * vehicle.speed

                    # 计算行驶能耗
                    consumption_rate = getattr(vehicle, 'consumption_rate', 0.35)
                    travel_energy = distance * consumption_rate

                    # 计算行驶过程中的制冷能耗
                    refr_energy = 0
                    if hasattr(vehicle, 'calculate_refrigeration_energy'):
                        refr_energy = vehicle.calculate_refrigeration_energy(travel_time, is_idle=False)
                    else:
                        # 如果没有计算方法，使用简化估算
                        refr_power = getattr(vehicle, 'refrigeration_power', 2.5)
                        refr_energy_ratio = getattr(vehicle, 'refr_energy_ratio', 0.7)
                        refr_energy = refr_power * (travel_time / 60.0) / refr_energy_ratio

                    # 总能耗
                    total_energy = travel_energy + refr_energy

                    # 为了平滑显示，添加多个中间点
                    num_points = max(5, int(travel_time / 5))  # 每5分钟一个点，至少5个点
                    time_step = travel_time / num_points
                    energy_step = total_energy / num_points

                    for j in range(1, num_points + 1):
                        interim_time = prev_node['departure_time'] + j * time_step
                        interim_battery = current_battery - j * energy_step
                        timeline['time'].append(interim_time)
                        timeline['battery_level'].append(interim_battery)

                    # 更新当前状态
                    current_time = curr_node['arrival_time']
                    current_battery -= total_energy

                    # 添加到达节点的信息
                    timeline['time'].append(current_time)
                    timeline['battery_level'].append(current_battery)

                    # 标记节点类型
                    if node_id == 0:
                        timeline['node_times'].append(current_time)
                        timeline['node_labels'].append("配送中心")
                    elif 1 <= node_id <= len(self.customers):
                        timeline['node_times'].append(current_time)
                        timeline['node_labels'].append(f"C{node_id}")
                    elif node_id > len(self.customers):
                        station_idx = node_id - len(self.customers) - 1
                        if station_idx < len(self.charging_stations):
                            timeline['charging_times'].append(current_time)
                            timeline['charging_labels'].append(f"CS{int(self.charging_stations[station_idx].id)}")

                # 2. 等待阶段
                waiting_time = curr_node['waiting_time']
                if waiting_time > 0:
                    # 计算等待期间的制冷能耗
                    waiting_refr_energy = 0
                    if hasattr(vehicle, 'calculate_refrigeration_energy'):
                        waiting_refr_energy = vehicle.calculate_refrigeration_energy(waiting_time, is_idle=True)
                    else:
                        # 简化估算
                        refr_power = getattr(vehicle, 'refrigeration_power', 2.5)
                        idle_ratio = getattr(vehicle, 'idle_refr_power_ratio', 0.4)
                        refr_energy_ratio = getattr(vehicle, 'refr_energy_ratio', 0.7)
                        waiting_refr_energy = refr_power * idle_ratio * (waiting_time / 60.0) / refr_energy_ratio

                    # 更新电量
                    current_battery -= waiting_refr_energy
                    current_time += waiting_time

                    # 添加等待后的状态
                    timeline['time'].append(current_time)
                    timeline['battery_level'].append(current_battery)

                # 3. 服务阶段
                service_time = curr_node['service_time']
                if service_time > 0:
                    # 计算服务期间的制冷能耗
                    service_refr_energy = 0
                    if hasattr(vehicle, 'calculate_refrigeration_energy'):
                        service_refr_energy = vehicle.calculate_refrigeration_energy(service_time, is_idle=True)
                    else:
                        # 简化估算
                        refr_power = getattr(vehicle, 'refrigeration_power', 2.5)
                        idle_ratio = getattr(vehicle, 'idle_refr_power_ratio', 0.4)
                        refr_energy_ratio = getattr(vehicle, 'refr_energy_ratio', 0.7)
                        service_refr_energy = refr_power * idle_ratio * (service_time / 60.0) / refr_energy_ratio

                    # 更新电量
                    current_battery -= service_refr_energy
                    current_time += service_time

                    # 添加服务后的状态
                    timeline['time'].append(current_time)
                    timeline['battery_level'].append(current_battery)

                # 4. 充电阶段（如果是充电站）
                if node_id > len(self.customers):
                    station_idx = node_id - len(self.customers) - 1
                    if station_idx < len(self.charging_stations):
                        charging_station = self.charging_stations[station_idx]

                        # 计算充电时间（从到达到离开的时间减去等待和服务时间）
                        charging_time = curr_node['departure_time'] - curr_node['arrival_time'] - curr_node['waiting_time'] - curr_node['service_time']

                        if charging_time > 0:
                            # 获取充电站充电速率
                            charging_rate = getattr(charging_station, 'charging_rate', 1.0)  # kWh/min

                            # 计算充电量
                            target_level = battery_capacity * getattr(vehicle, 'partial_charging_factor', 0.8)
                            max_charge = target_level - current_battery
                            charge_amount = min(charging_time * charging_rate, max_charge)

                            if charge_amount > 0:
                                # 添加多个点来平滑显示充电过程
                                num_points = max(10, int(charging_time / 3))  # 每3分钟一个点，至少10个点
                                time_step = charging_time / num_points
                                energy_step = charge_amount / num_points

                                # 充电开始时间
                                charge_start_time = current_time

                                for j in range(1, num_points + 1):
                                    interim_time = charge_start_time + j * time_step
                                    interim_battery = current_battery + j * energy_step
                                    timeline['time'].append(interim_time)
                                    timeline['battery_level'].append(interim_battery)

                                # 更新电量和时间
                                current_battery += charge_amount
                                current_time += charging_time

                                # 添加充电后的状态
                                timeline['time'].append(current_time)
                                timeline['battery_level'].append(current_battery)
        else:
            # 如果没有详细的调度信息，使用简化的模拟
            for i in range(1, len(route)):
                from_node = route[i-1]
                to_node = route[i]

                # 获取节点坐标
                from_x, from_y = self._get_node_coordinates(from_node)
                to_x, to_y = self._get_node_coordinates(to_node)

                # 计算距离
                distance = np.sqrt((to_x - from_x)**2 + (to_y - from_y)**2)

                # 获取车辆参数
                speed = getattr(vehicle, 'speed', 45.0)
                consumption_rate = getattr(vehicle, 'consumption_rate', 0.35)

                # 计算行驶时间和能耗
                travel_time = distance / speed * 60  # 分钟
                energy_consumption = distance * consumption_rate

                # 更新时间和电量
                current_time += travel_time
                current_battery -= energy_consumption

                # 添加到达点的信息
                timeline['time'].append(current_time)
                timeline['battery_level'].append(current_battery)

                # 标记节点类型
                if to_node == 0:
                    timeline['node_times'].append(current_time)
                    timeline['node_labels'].append("配送中心")
                elif 1 <= to_node <= len(self.customers):
                    timeline['node_times'].append(current_time)
                    timeline['node_labels'].append(f"C{to_node}")

                    # 如果是客户点，添加服务时间和制冷能耗
                    customer = self.customers[to_node-1]
                    service_time = getattr(customer, 'service_time', 30)

                    # 计算服务期间的制冷能耗
                    refr_power = getattr(vehicle, 'refrigeration_power', 2.5)
                    idle_ratio = getattr(vehicle, 'idle_refr_power_ratio', 0.4)
                    refr_energy_ratio = getattr(vehicle, 'refr_energy_ratio', 0.7)
                    refr_energy = refr_power * idle_ratio * (service_time / 60.0) / refr_energy_ratio

                    # 更新时间和电量
                    current_time += service_time
                    current_battery -= refr_energy

                    # 添加服务后的状态
                    timeline['time'].append(current_time)
                    timeline['battery_level'].append(current_battery)
                elif to_node > len(self.customers):
                    station_idx = to_node - len(self.customers) - 1
                    if station_idx < len(self.charging_stations):
                        charging_station = self.charging_stations[station_idx]

                        # 标记充电站
                        timeline['charging_times'].append(current_time)
                        timeline['charging_labels'].append(f"CS{int(charging_station.id)}")

                        # 计算充电量和时间
                        charging_rate = getattr(charging_station, 'charging_rate', 1.0)
                        target_level = battery_capacity * getattr(vehicle, 'partial_charging_factor', 0.8)
                        charge_amount = max(0, target_level - current_battery)
                        charging_time = charge_amount / charging_rate

                        if charging_time > 0 and charge_amount > 0:
                            # 添加多个点来平滑显示充电过程
                            num_points = max(10, int(charging_time / 3))
                            time_step = charging_time / num_points
                            energy_step = charge_amount / num_points

                            for j in range(1, num_points + 1):
                                interim_time = current_time + j * time_step
                                interim_battery = current_battery + j * energy_step
                                timeline['time'].append(interim_time)
                                timeline['battery_level'].append(interim_battery)

                            # 更新时间和电量
                            current_time += charging_time
                            current_battery += charge_amount

                            # 添加充电后的状态
                            timeline['time'].append(current_time)
                            timeline['battery_level'].append(current_battery)

        return timeline

    def _get_node_coordinates(self, node_id):
        """
        获取节点坐标

        参数:
            node_id (int): 节点ID

        返回:
            tuple: (x, y) 坐标
        """
        if node_id == 0:
            return self.depot.x, self.depot.y
        elif 1 <= node_id <= len(self.customers):
            return self.customers[node_id-1].x, self.customers[node_id-1].y
        else:
            station_idx = node_id - len(self.customers) - 1
            if station_idx < len(self.charging_stations):
                return self.charging_stations[station_idx].x, self.charging_stations[station_idx].y
            else:
                # 如果无效，返回原点
                print(f"警告: 无效的节点ID {node_id}")
                return 0, 0

    def plot_battery_levels(self, show=False):
        """
        绘制车辆电量随配送时长变化曲线

        参数:
            show (bool): 是否显示图表

        返回:
            list: 保存路径列表
        """
        saved_paths = []
        active_routes = [i for i, route in enumerate(self.solution.vehicle_routes) if len(route) > 2]

        # 获取总充电量和充电时间信息（不再打印到控制台）
        total_charge_time = 0
        for vehicle in self.vehicles:
            if hasattr(vehicle, 'total_charging_time') and vehicle.total_charging_time > 0:
                total_charge_time += vehicle.total_charging_time

        for vehicle_idx in active_routes:
            route = self.solution.vehicle_routes[vehicle_idx]

            # 确保索引有效
            if vehicle_idx >= len(self.vehicles):
                print(f"警告: 无效的车辆索引 {vehicle_idx}，跳过绘制电量曲线")
                continue

            vehicle = self.vehicles[vehicle_idx]

            # 不再输出路径信息，避免重复输出
            # 路径信息已经在main.py中打印

            # 检查路径中是否存在充电站（不再打印信息）
            # 只在内部使用，不输出到控制台
            charging_stations_in_route = []
            for node in route:
                if node > len(self.customers):
                    station_idx = node - len(self.customers) - 1
                    if station_idx < len(self.charging_stations):
                        charging_stations_in_route.append(self.charging_stations[station_idx].id)

            # 生成电量时间线数据
            timeline = self._generate_battery_timeline(vehicle, route)

            # 确保时间点是按顺序排列的
            # 创建时间和电量的配对，并按时间排序
            time_battery_pairs = sorted(zip(timeline['time'], timeline['battery_level']))

            # 分离排序后的时间和电量
            sorted_times, sorted_battery_levels = zip(*time_battery_pairs) if time_battery_pairs else ([], [])

            # 移除重复的时间点（保留最后一个值）
            unique_times = []
            unique_battery_levels = []
            last_time = -1

            for t, b in zip(sorted_times, sorted_battery_levels):
                if t != last_time:
                    unique_times.append(t)
                    unique_battery_levels.append(b)
                    last_time = t
                else:
                    # 如果时间点重复，更新最后一个电量值
                    unique_battery_levels[-1] = b

            # 转换为小时
            times = [t/60.0 for t in unique_times]
            battery_levels = unique_battery_levels

            # 处理客户点和充电站时间
            # 创建时间和标签的配对，并按时间排序
            customer_pairs = sorted(zip(timeline['node_times'], timeline['node_labels']))
            charging_pairs = sorted(zip(timeline['charging_times'], timeline['charging_labels']))

            # 分离排序后的时间和标签
            customer_times = [t/60.0 for t, _ in customer_pairs]
            customer_labels = [label for _, label in customer_pairs]
            charging_times = [t/60.0 for t, _ in charging_pairs]
            charging_labels = [label for _, label in charging_pairs]

            # 创建图表
            plt.figure(figsize=(14, 8))

            # 绘制电量变化曲线
            plt.plot(times, battery_levels, 'b-', linewidth=2)

            # 安全获取电池容量
            battery_capacity = getattr(vehicle, 'battery_capacity', 40.0)

            # 添加电池容量水平线
            plt.axhline(y=battery_capacity, color='r', linestyle='--',
                        label=f'电池容量: {battery_capacity} kWh')

            # 添加80%充电水平线（如果使用部分充电策略）
            if hasattr(vehicle, 'partial_charging_factor'):
                partial_level = battery_capacity * vehicle.partial_charging_factor
                plt.axhline(y=partial_level, color='g', linestyle='--',
                            label=f'部分充电水平: {partial_level:.1f} kWh ({vehicle.partial_charging_factor*100:.0f}%)')

            # 添加0电量水平线
            plt.axhline(y=0, color='r', linestyle='-', alpha=0.3)

            # 绘制客户点垂直线
            for t, label in zip(customer_times, customer_labels):
                if label == "配送中心":
                    plt.axvline(x=t, color='red', linestyle=':', alpha=0.5)
                    plt.text(t, battery_capacity*0.9, "配送中心",
                         horizontalalignment='center', verticalalignment='bottom',
                         rotation=0, fontsize=8, bbox=dict(facecolor='white', alpha=0.6))
                else:
                    plt.axvline(x=t, color='blue', linestyle=':', alpha=0.5)
                    plt.text(t, battery_capacity*0.8, label,
                         horizontalalignment='center', verticalalignment='bottom',
                         rotation=0, fontsize=8, bbox=dict(facecolor='white', alpha=0.6))

            # 绘制充电站垂直线
            for t, label in zip(charging_times, charging_labels):
                plt.axvline(x=t, color='green', linestyle=':', alpha=0.7)
                plt.text(t, battery_capacity*0.7, label,
                     horizontalalignment='center', verticalalignment='bottom',
                     rotation=0, fontsize=8, color='green',
                     bbox=dict(facecolor='white', alpha=0.6))

            # 添加图例
            plt.legend(loc='upper right')

            # 添加标题
            plt.title(f'车辆 {vehicle_idx+1} 电量随时间变化')

            # 添加轴标签
            plt.xlabel('配送时长 (小时)')
            plt.ylabel('电池电量 (kWh)')

            # 添加网格
            plt.grid(True, linestyle='--', alpha=0.7)

            # 设置y轴范围，确保0点可见
            plt.ylim(-1, battery_capacity * 1.1)

            # 调整布局
            plt.tight_layout()

            # 保存图表
            save_path = os.path.join(self.output_dir, f"Car_{vehicle_idx+1}_battery.png")
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            saved_paths.append(save_path)

            # 显示图表
            if show:
                plt.show()
            else:
                plt.close()

        return saved_paths


def visualize_data(solution, depot, customers, charging_stations, vehicles, output_dir=None):
    """
    生成可视化图表

    参数:
        solution (Solution): 解决方案对象
        depot (Customer): 配送中心对象
        customers (list): 客户对象列表
        charging_stations (list): 充电站对象列表
        vehicles (list): 车辆对象列表
        output_dir (str, optional): 输出目录

    返回:
        dict: 包含各种图表保存路径的字典
    """
    visualizer = Visualizer(solution, depot, customers, charging_stations, vehicles, output_dir)

    # 生成路径图
    route_map_path = visualizer.plot_routes(show=False)

    # 生成电量变化曲线图
    battery_chart_paths = visualizer.plot_battery_levels(show=False)

    result = {
        'route_map': route_map_path,
        'battery_charts': battery_chart_paths
    }

    print(f"可视化图表已生成，保存至: {visualizer.output_dir}")
    return result


if __name__ == "__main__":
    print("此模块不适合直接运行，请从main.py调用")