"""
主程序入口，实现完整的部分充电策略电动汽车冷链物流配送路径优化流程
作者：[您的姓名]
日期：[当前日期]
"""
import os
import time
import random
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import sys
import copy

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入调试模块
from src.debug_time_windows import debug_time_windows
from src.debug_charging import debug_charging

from src.models.solution import Solution
from src.models.vehicle import Vehicle
from src.models.customer import Customer
from src.models.charging import ChargingStation

from src.algorithms.genetic import GeneticAlgorithm
from src.utils.data_loader import load_test_data, generate_test_data, save_test_data
from src.utils.visualization import plot_solution, plot_convergence, plot_cost_breakdown, plot_comparison

from src.config import GA_CONFIG, COST_PARAMS, VEHICLE_PARAMS, EXPERIMENT_CONFIG, FILE_PATHS

# 导入新增的数据收集和可视化模块
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "analysis"))
try:
    from analysis.collect_data import collect_data
    from analysis.visualize import visualize_data
    HAS_ANALYSIS_MODULES = True
except ImportError:
    print("警告: 无法导入数据分析模块，将不会生成详细数据报告")
    HAS_ANALYSIS_MODULES = False


def setup_directories():
    """创建必要的目录"""
    os.makedirs(FILE_PATHS['results_dir'], exist_ok=True)
    os.makedirs(FILE_PATHS['plots_dir'], exist_ok=True)
    os.makedirs(FILE_PATHS['logs_dir'], exist_ok=True)
    os.makedirs('data', exist_ok=True)
    # 为数据分析模块创建输出目录
    os.makedirs(os.path.join('analysis', 'outputs'), exist_ok=True)


def run_experiment(ga_config=None, vehicle_params=None, cost_params=None,
                  customer_file=None, charging_station_file=None, seed=None,
                  output_prefix='exp'):
    """
    运行单次实验

    参数:
        ga_config (dict): 遗传算法配置
        vehicle_params (dict): 车辆参数
        cost_params (dict): 成本参数
        customer_file (str): 客户数据文件
        charging_station_file (str): 充电站数据文件
        seed (int): 随机种子
        output_prefix (str): 输出文件前缀

    返回:
        dict: 实验结果
    """
    # 打印传入的参数
    print("\n===== 实验开始，传入参数检查 =====")
    if vehicle_params:
        print(f"传入的max_load: {vehicle_params['max_load']}")
        print(f"传入的battery_capacity: {vehicle_params['battery_capacity']}")
    else:
        print("未传入vehicle_params，将使用默认配置")
    print("===================================\n")

    # 设置默认参数并进行深拷贝
    if ga_config is None:
        ga_config = copy.deepcopy(GA_CONFIG)
    else:
        ga_config = copy.deepcopy(ga_config)

    if vehicle_params is None:
        vehicle_params = copy.deepcopy(VEHICLE_PARAMS)
    else:
        vehicle_params = copy.deepcopy(vehicle_params)

    if cost_params is None:
        cost_params = copy.deepcopy(COST_PARAMS)
    else:
        cost_params = copy.deepcopy(cost_params)

    if customer_file is None:
        customer_file = FILE_PATHS['customer_data']
    if charging_station_file is None:
        charging_station_file = FILE_PATHS['charging_station_data']

    # 设置随机种子
    if seed is not None:
        random.seed(seed)
        np.random.seed(seed)

    # 开始计时
    start_time = time.time()

    # 加载数据
    depot, customers, charging_stations, vehicles, distance_matrix = load_test_data(
        customer_file, charging_station_file, vehicle_params
    )

    # 创建遗传算法
    ga = GeneticAlgorithm(
        vehicles=vehicles,
        customers=customers,
        charging_stations=charging_stations,
        distance_matrix=distance_matrix,
        solution_class=Solution,
        population_size=ga_config['population_size'],
        max_generations=ga_config['max_generations'],
        elite_size=ga_config['elite_size'],
        crossover_rate=ga_config['crossover_rate'],
        mutation_rate=ga_config['mutation_rate'],
        selection_method=ga_config['selection_method'],
        crossover_method=ga_config['crossover_method'],
        mutation_method=ga_config['mutation_method']
    )

    # 运行算法
    print(f"\n{'='*50}")
    print(f"开始运行实验: {output_prefix}")
    print(f"{'='*50}")
    best_solution = ga.run(verbose=True, early_stopping=EXPERIMENT_CONFIG['early_stopping'])

    # 获取结果摘要
    results = ga.get_results_summary()

    # 计算总运行时间
    total_runtime = time.time() - start_time
    results['total_runtime'] = total_runtime

    # 调试时间窗口计算
    print("\n开始调试时间窗口计算...")
    debug_time_windows(best_solution)

    # 调试充电计算
    print("\n开始调试充电计算...")
    debug_charging(best_solution)

    # 绘制结果图
    # 1. 路径图
    plot_path = os.path.join(FILE_PATHS['plots_dir'], f"{output_prefix}_route.png")
    plot_solution(best_solution, depot, customers, charging_stations,
                  title=f"{output_prefix} - 最优路径", show=False, save_path=plot_path)

    # 2. 收敛曲线
    convergence_path = os.path.join(FILE_PATHS['plots_dir'], f"{output_prefix}_convergence.png")
    plot_convergence(ga.best_fitness_history, ga.avg_fitness_history,
                    title=f"{output_prefix} - 收敛曲线", show=False, save_path=convergence_path)

    # 3. 成本分解
    cost_path = os.path.join(FILE_PATHS['plots_dir'], f"{output_prefix}_costs.png")
    plot_cost_breakdown(best_solution, show=False, save_path=cost_path)

    # 保存详细结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_file = os.path.join(FILE_PATHS['logs_dir'], f"{output_prefix}_{timestamp}.txt")

    with open(result_file, 'w', encoding='utf-8') as f:
        f.write(f"实验: {output_prefix}\n")
        f.write(f"日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总运行时间: {total_runtime:.2f}秒\n\n")

        f.write("遗传算法参数:\n")
        for key, value in ga_config.items():
            f.write(f"  {key}: {value}\n")
        f.write("\n")

        f.write("车辆参数:\n")
        for key, value in vehicle_params.items():
            f.write(f"  {key}: {value}\n")
        f.write("\n")

        f.write("成本参数:\n")
        for key, value in cost_params.items():
            f.write(f"  {key}: {value}\n")
        f.write("\n")

        f.write("优化结果:\n")
        f.write(f"  总成本: {best_solution.fitness:.2f}\n")
        f.write(f"  固定成本: {best_solution.fixed_cost:.2f}\n")
        f.write(f"  距离成本: {best_solution.distance_cost:.2f}\n")
        f.write(f"  充电成本: {best_solution.charging_cost:.2f}\n")
        f.write(f"  制冷成本: {best_solution.refrigeration_cost:.2f}\n")
        if hasattr(best_solution, 'early_arrival_penalty') and best_solution.early_arrival_penalty > 0:
            f.write(f"  提前到达惩罚: {best_solution.early_arrival_penalty:.2f}\n")
        if hasattr(best_solution, 'late_arrival_penalty') and best_solution.late_arrival_penalty > 0:
            f.write(f"  延误到达惩罚: {best_solution.late_arrival_penalty:.2f}\n")
        f.write(f"  使用车辆数: {sum(1 for route in best_solution.vehicle_routes if len(route) > 2)}\n")
        f.write(f"  总行驶距离: {sum(vehicle.total_distance for vehicle in best_solution.vehicles if vehicle.total_distance > 0):.2f}km\n")

        # 记录路径详情
        f.write("\n路径详情:\n")

        # 直接参考路径绘制代码的方式记录路径
        for i, route in enumerate(best_solution.vehicle_routes):
            if len(route) <= 2:  # 跳过空路径
                continue

            f.write(f"  车辆 {i+1}:")

            # 遍历路径中的每个节点
            for node in route:
                if node == 0:  # 配送中心
                    f.write(" 配送中心")
                elif 1 <= node <= len(customers):  # 客户
                    f.write(f" 客户{node}")
                else:  # 充电站
                    station_idx = node - len(customers) - 1
                    f.write(f" 充电站{charging_stations[station_idx].id}")
            f.write("\n")

        # 打印路径信息到控制台
        print("\n车辆路线规划:")

        # 完全参照visualization.py中的路径绘制代码
        for i, route in enumerate(best_solution.vehicle_routes):
            if len(route) <= 2:  # 跳过空路径（只有depot-depot）
                continue

            route_str = f"车辆 {i+1}: 配送中心"

            # 遍历路径中的所有节点（包括起点和终点）
            for node_id in route:
                if node_id == 0:  # 配送中心
                    # 起点已经添加了，所以只为非起点的配送中心添加
                    if route_str != f"车辆 {i+1}: 配送中心":
                        route_str += " -> 配送中心"
                elif 1 <= node_id <= len(customers):  # 客户
                    route_str += f" -> C{node_id}"
                else:  # 充电站
                    station_idx = node_id - len(customers) - 1
                    route_str += f" -> 充电站{charging_stations[station_idx].id}"

            # 确保路径返回配送中心（如果最后一个节点不是配送中心）
            if route[-1] != 0:
                route_str += " -> 配送中心"

            print(route_str)

    print(f"\n实验 {output_prefix} 完成")
    print(f"结果已保存至: {result_file}")
    print(f"图表已保存至: {FILE_PATHS['plots_dir']}")

    # 使用新增模块进行数据收集和可视化
    if HAS_ANALYSIS_MODULES:
        print("\n开始生成详细数据分析报告...")

        # 创建统一的输出目录
        output_timestamp = datetime.now().strftime("%Y/%m/%d/%H:%M:%S")
        analysis_output_dir = os.path.join("analysis/outputs", output_timestamp.replace(':', '_'))
        os.makedirs(analysis_output_dir, exist_ok=True)

        # 收集数据
        report_path = collect_data(
            best_solution, depot, customers, charging_stations, best_solution.vehicles,
            ga_config, vehicle_params, cost_params, total_runtime
        )

        # 生成可视化
        viz_results = visualize_data(
            best_solution, depot, customers, charging_stations, best_solution.vehicles,
            output_dir=os.path.dirname(report_path)  # 使用相同目录
        )

        print(f"\n详细数据分析报告已生成:")
        print(f"- 数据报告: {report_path}")
        print(f"- 路径图: {viz_results['route_map']}")
        print(f"- 电量曲线图: {len(viz_results['battery_charts'])} 个车辆电量变化图")

    return results


def generate_data_if_needed():
    """生成测试数据（如果不存在）"""
    if not os.path.exists(FILE_PATHS['customer_data']) or not os.path.exists(FILE_PATHS['charging_station_data']):
        print("未找到测试数据，正在生成...")

        # 生成测试数据
        customer_df, charging_station_df = generate_test_data(
            num_customers=30,
            num_charging_stations=5,
            area_size=100,
            demand_range=(10, 200),
            time_window_width=60,
            seed=EXPERIMENT_CONFIG['random_seed']
        )

        # 保存测试数据
        save_test_data(
            customer_df,
            charging_station_df,
            FILE_PATHS['customer_data'],
            FILE_PATHS['charging_station_data']
        )

        print(f"测试数据已生成并保存至:\n- {FILE_PATHS['customer_data']}\n- {FILE_PATHS['charging_station_data']}")


def compare_charging_strategies():
    """比较不同充电策略的性能"""
    print("\n正在比较不同充电策略...")

    # 定义不同的充电策略
    strategies = [
        {
            'name': '全充电策略',
            'partial_charging_factor': 1.0,
            'prefix': 'full_charging'
        },
        {
            'name': '部分充电策略',
            'partial_charging_factor': 0.8,
            'prefix': 'partial_charging'
        },
        {
            'name': '最小充电策略',
            'partial_charging_factor': 0.6,
            'prefix': 'minimal_charging'
        }
    ]

    results = []

    # 为每种策略运行实验
    for strategy in strategies:
        # 创建车辆参数深拷贝
        params = copy.deepcopy(VEHICLE_PARAMS)
        params['partial_charging_factor'] = strategy['partial_charging_factor']

        # 运行实验
        result = run_experiment(
            vehicle_params=params,
            output_prefix=strategy['prefix'],
            seed=EXPERIMENT_CONFIG['random_seed']
        )

        result['name'] = strategy['name']
        results.append(result)

    # 比较结果
    labels = [result['name'] for result in results]

    # 比较总成本
    cost_comparison_path = os.path.join(FILE_PATHS['plots_dir'], "strategy_cost_comparison.png")
    plot_comparison(results, metric='best_fitness', labels=labels,
                   title='不同充电策略的总成本对比', show=False, save_path=cost_comparison_path)

    # 比较距离成本
    distance_comparison_path = os.path.join(FILE_PATHS['plots_dir'], "strategy_distance_comparison.png")
    plot_comparison(results, metric='distance_cost', labels=labels,
                   title='不同充电策略的距离成本对比', show=False, save_path=distance_comparison_path)

    # 比较充电成本
    charging_comparison_path = os.path.join(FILE_PATHS['plots_dir'], "strategy_charging_comparison.png")
    plot_comparison(results, metric='charging_cost', labels=labels,
                   title='不同充电策略的充电成本对比', show=False, save_path=charging_comparison_path)

    # 比较执行时间
    time_comparison_path = os.path.join(FILE_PATHS['plots_dir'], "strategy_time_comparison.png")
    plot_comparison(results, metric='execution_time', labels=labels,
                   title='不同充电策略的执行时间对比', show=False, save_path=time_comparison_path)

    # 保存对比结果
    comparison_file = os.path.join(FILE_PATHS['logs_dir'], "strategy_comparison.txt")
    with open(comparison_file, 'w', encoding='utf-8') as f:
        f.write("不同充电策略对比结果\n")
        f.write(f"日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        for result in results:
            f.write(f"策略: {result['name']}\n")
            f.write(f"  总成本: {result['best_fitness']:.2f}\n")
            f.write(f"  固定成本: {result['fixed_cost']:.2f}\n")
            f.write(f"  距离成本: {result['distance_cost']:.2f}\n")
            f.write(f"  充电成本: {result['charging_cost']:.2f}\n")
            f.write(f"  制冷成本: {result['refrigeration_cost']:.2f}\n")
            if 'early_arrival_penalty' in result and result['early_arrival_penalty'] > 0:
                f.write(f"  提前到达惩罚: {result['early_arrival_penalty']:.2f}\n")
            if 'late_arrival_penalty' in result and result['late_arrival_penalty'] > 0:
                f.write(f"  延误到达惩罚: {result['late_arrival_penalty']:.2f}\n")
            f.write(f"  使用车辆数: {result['used_vehicles']}\n")
            f.write(f"  总行驶距离: {result['total_distance']:.2f}km\n")
            f.write(f"  执行时间: {result['execution_time']:.2f}秒\n\n")

        # 计算部分充电相对于全充电的成本节省比例
        if len(results) >= 2:
            full_cost = results[0]['best_fitness']
            partial_cost = results[1]['best_fitness']
            saving_ratio = (full_cost - partial_cost) / full_cost * 100

            f.write(f"部分充电策略相比全充电策略节省了 {saving_ratio:.2f}% 的总成本\n")

    print(f"策略对比已完成，结果保存至: {comparison_file}")


def main():
    """主函数，程序入口"""
    # 创建必要的目录
    setup_directories()

    # 创建测试数据
    if not os.path.exists(FILE_PATHS['customer_data']) or not os.path.exists(FILE_PATHS['charging_station_data']):
        print("生成测试数据...")
        customer_df, charging_station_df = generate_test_data(
            num_customers=30,
            num_charging_stations=5,
            seed=EXPERIMENT_CONFIG['random_seed']
        )
        save_test_data(
            customer_df,
            charging_station_df,
            FILE_PATHS['customer_data'],
            FILE_PATHS['charging_station_data']
        )
        print(f"测试数据已保存至: {FILE_PATHS['customer_data']} 和 {FILE_PATHS['charging_station_data']}")

    # 运行基线实验
    run_experiment(output_prefix='baseline', vehicle_params=VEHICLE_PARAMS, seed=EXPERIMENT_CONFIG['random_seed'])


if __name__ == "__main__":
    main()