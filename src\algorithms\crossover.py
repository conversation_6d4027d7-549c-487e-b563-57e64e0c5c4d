"""
交叉操作模块，实现多种交叉策略
"""
import random
import numpy as np
import copy


class CrossoverOperator:
    """交叉操作类，支持多种交叉策略"""
    
    @staticmethod
    def order_crossover(parent1, parent2, solution_class):
        """
        顺序交叉 (OX)
        
        参数:
            parent1 (Solution): 父解1
            parent2 (Solution): 父解2
            solution_class: 解决方案类
            
        返回:
            tuple: 两个子代解决方案
        """
        # 深拷贝父代染色体
        p1_chromosome = copy.deepcopy(parent1.chromosome)
        p2_chromosome = copy.deepcopy(parent2.chromosome)
        
        # 获取客户数量
        n_customers = len(parent1.customers)
        
        # 分离客户部分和车辆分隔符部分
        p1_customers = [gene for gene in p1_chromosome if gene <= n_customers]
        p1_delimiters = [gene for gene in p1_chromosome if gene > n_customers]
        
        p2_customers = [gene for gene in p2_chromosome if gene <= n_customers]
        p2_delimiters = [gene for gene in p2_chromosome if gene > n_customers]
        
        # 如果客户部分长度为0，直接返回父代
        if not p1_customers or not p2_customers:
            return copy.deepcopy(parent1), copy.deepcopy(parent2)
        
        # 随机选择两个交叉点
        cx_points = sorted(random.sample(range(len(p1_customers)), 2) if len(p1_customers) >= 2 else [0, len(p1_customers)])
        
        # 创建OX后代
        def create_ox_offspring(parent_cust, other_parent_cust, cx_pts):
            # 选取交叉点之间的段
            mid_segment = parent_cust[cx_pts[0]:cx_pts[1]]
            
            # 初始化子代
            offspring = [None] * len(parent_cust)
            
            # 复制交叉点之间的段
            offspring[cx_pts[0]:cx_pts[1]] = mid_segment
            
            # 从第二个交叉点开始填充剩余位置
            idx = cx_pts[1]
            for gene in other_parent_cust[cx_pts[1]:] + other_parent_cust[:cx_pts[1]]:
                if gene not in mid_segment:
                    if idx >= len(offspring):
                        idx = 0
                    while idx < len(offspring) and offspring[idx] is not None:
                        idx = (idx + 1) % len(offspring)
                    if idx < len(offspring):
                        offspring[idx] = gene
                        idx = (idx + 1) % len(offspring)
            
            # 确保没有None值
            for i in range(len(offspring)):
                if offspring[i] is None:
                    # 找一个未使用的客户填充
                    for cust in range(1, n_customers + 1):
                        if cust not in offspring:
                            offspring[i] = cust
                            break
            
            return offspring
        
        # 创建两个子代
        o1_customers = create_ox_offspring(p1_customers, p2_customers, cx_points)
        o2_customers = create_ox_offspring(p2_customers, p1_customers, cx_points)
        
        # 处理车辆分隔符 - 简单地继承父代的分隔符
        o1_delimiters = p1_delimiters[:] if p1_delimiters else []
        o2_delimiters = p2_delimiters[:] if p2_delimiters else []
        
        # 创建后代染色体
        o1_chromosome = o1_customers + o1_delimiters
        o2_chromosome = o2_customers + o2_delimiters
        
        # 创建后代解决方案对象
        offspring1 = copy.deepcopy(parent1)
        offspring2 = copy.deepcopy(parent2)
        
        # 解码染色体
        offspring1.decode(o1_chromosome)
        offspring2.decode(o2_chromosome)
        
        return offspring1, offspring2
    
    @staticmethod
    def partially_mapped_crossover(parent1, parent2, solution_class):
        """
        部分映射交叉 (PMX)
        
        参数:
            parent1 (Solution): 父解1
            parent2 (Solution): 父解2
            solution_class: 解决方案类
            
        返回:
            tuple: 两个子代解决方案
        """
        # 深拷贝父代染色体
        p1_chromosome = copy.deepcopy(parent1.chromosome)
        p2_chromosome = copy.deepcopy(parent2.chromosome)
        
        # 获取客户数量
        n_customers = len(parent1.customers)
        
        # 分离客户部分和车辆分隔符部分
        p1_customers = [gene for gene in p1_chromosome if gene <= n_customers]
        p1_delimiters = [gene for gene in p1_chromosome if gene > n_customers]
        
        p2_customers = [gene for gene in p2_chromosome if gene <= n_customers]
        p2_delimiters = [gene for gene in p2_chromosome if gene > n_customers]
        
        # 如果客户部分长度为0，直接返回父代
        if not p1_customers or not p2_customers:
            return copy.deepcopy(parent1), copy.deepcopy(parent2)
        
        # 随机选择两个交叉点
        cx_points = sorted(random.sample(range(len(p1_customers)), 2) if len(p1_customers) >= 2 else [0, len(p1_customers)])
        
        # 创建PMX后代
        def create_pmx_offspring(parent1_cust, parent2_cust, cx_pts):
            # 初始化子代
            offspring = [None] * len(parent1_cust)
            
            # 复制交叉点之间的段
            offspring[cx_pts[0]:cx_pts[1]] = parent1_cust[cx_pts[0]:cx_pts[1]]
            
            # 创建映射关系
            mapping = {}
            for i in range(cx_pts[0], cx_pts[1]):
                if i < len(parent1_cust) and i < len(parent2_cust) and parent1_cust[i] != parent2_cust[i]:
                    mapping[parent2_cust[i]] = parent1_cust[i]
            
            # 填充剩余位置
            for i in range(len(offspring)):
                if i < cx_pts[0] or i >= cx_pts[1]:
                    if i < len(parent2_cust):
                        # 获取对应位置的父代2基因
                        gene = parent2_cust[i]
                        
                        # 检查是否已在子代中
                        while gene in offspring[cx_pts[0]:cx_pts[1]]:
                            gene = mapping.get(gene, gene)
                        
                        offspring[i] = gene
            
            # 确保没有None值
            for i in range(len(offspring)):
                if offspring[i] is None:
                    # 找一个未使用的客户填充
                    for cust in range(1, n_customers + 1):
                        if cust not in offspring:
                            offspring[i] = cust
                            break
            
            return offspring
        
        # 创建两个子代
        o1_customers = create_pmx_offspring(p1_customers, p2_customers, cx_points)
        o2_customers = create_pmx_offspring(p2_customers, p1_customers, cx_points)
        
        # 处理车辆分隔符
        o1_delimiters = p1_delimiters[:]  # 简单地复制父代1的分隔符
        o2_delimiters = p2_delimiters[:]  # 简单地复制父代2的分隔符
        
        # 创建后代染色体
        o1_chromosome = o1_customers + o1_delimiters
        o2_chromosome = o2_customers + o2_delimiters
        
        # 创建后代解决方案对象
        offspring1 = copy.deepcopy(parent1)
        offspring2 = copy.deepcopy(parent2)
        
        # 解码染色体
        offspring1.decode(o1_chromosome)
        offspring2.decode(o2_chromosome)
        
        return offspring1, offspring2
    
    @staticmethod
    def edge_recombination_crossover(parent1, parent2, solution_class):
        """
        边重组交叉 (ERX)
        
        参数:
            parent1 (Solution): 父解1
            parent2 (Solution): 父解2
            solution_class: 解决方案类
            
        返回:
            tuple: 两个子代解决方案
        """
        # 深拷贝父代染色体
        p1_chromosome = copy.deepcopy(parent1.chromosome)
        p2_chromosome = copy.deepcopy(parent2.chromosome)
        
        # 获取客户数量
        n_customers = len(parent1.customers)
        
        # 分离客户部分和车辆分隔符部分
        p1_customers = [gene for gene in p1_chromosome if gene <= n_customers]
        p1_delimiters = [gene for gene in p1_chromosome if gene > n_customers]
        
        p2_customers = [gene for gene in p2_chromosome if gene <= n_customers]
        p2_delimiters = [gene for gene in p2_chromosome if gene > n_customers]
        
        # 如果客户部分长度为0，直接返回父代
        if not p1_customers or not p2_customers:
            return copy.deepcopy(parent1), copy.deepcopy(parent2)
        
        # 创建边邻接表
        edge_map = {}
        for i in range(len(p1_customers)):
            gene = p1_customers[i]
            prev_gene = p1_customers[i-1] if i > 0 else p1_customers[-1]
            next_gene = p1_customers[(i+1) % len(p1_customers)]
            
            if gene not in edge_map:
                edge_map[gene] = set()
            
            edge_map[gene].add(prev_gene)
            edge_map[gene].add(next_gene)
        
        for i in range(len(p2_customers)):
            gene = p2_customers[i]
            prev_gene = p2_customers[i-1] if i > 0 else p2_customers[-1]
            next_gene = p2_customers[(i+1) % len(p2_customers)]
            
            if gene not in edge_map:
                edge_map[gene] = set()
            
            edge_map[gene].add(prev_gene)
            edge_map[gene].add(next_gene)
        
        # 创建ERX后代
        def create_erx_offspring(start_gene):
            # 初始化子代
            offspring = [start_gene]
            
            # 已使用的基因
            used_genes = {start_gene}
            
            # 构建路径
            while len(offspring) < len(p1_customers):
                current = offspring[-1]
                
                # 获取相邻的未使用的基因
                if current in edge_map:
                    neighbors = [n for n in edge_map[current] if n not in used_genes]
                else:
                    neighbors = []
                
                if neighbors:
                    # 选择相邻基因中具有最少连接的基因
                    next_gene = min(neighbors, key=lambda x: len([n for n in edge_map.get(x, set()) if n not in used_genes]) if x in edge_map else float('inf'))
                else:
                    # 如果没有相邻的未使用基因，随机选择一个未使用的基因
                    unused_genes = [g for g in p1_customers if g not in used_genes]
                    if not unused_genes:
                        break
                    next_gene = random.choice(unused_genes)
                
                offspring.append(next_gene)
                used_genes.add(next_gene)
            
            return offspring
        
        # 从父代1和父代2的不同起点开始创建两个子代
        start1 = p1_customers[0] if p1_customers else 1
        start2 = p2_customers[0] if p2_customers else 1
        
        o1_customers = create_erx_offspring(start1)
        o2_customers = create_erx_offspring(start2)
        
        # 处理车辆分隔符
        o1_delimiters = p1_delimiters[:]  # 简单地复制父代1的分隔符
        o2_delimiters = p2_delimiters[:]  # 简单地复制父代2的分隔符
        
        # 创建后代染色体
        o1_chromosome = o1_customers + o1_delimiters
        o2_chromosome = o2_customers + o2_delimiters
        
        # 创建后代解决方案对象
        offspring1 = copy.deepcopy(parent1)
        offspring2 = copy.deepcopy(parent2)
        
        # 解码染色体
        offspring1.decode(o1_chromosome)
        offspring2.decode(o2_chromosome)
        
        return offspring1, offspring2
    
    @staticmethod
    def adaptive_crossover(parent1, parent2, solution_class, cost_params):
        """
        自适应交叉：根据父代特性自动选择最合适的交叉算子
        
        参数:
            parent1 (Solution): 父解1
            parent2 (Solution): 父解2
            solution_class: 解决方案类
            cost_params (dict): 成本参数
            
        返回:
            tuple: 两个子代解决方案
        """
        # 尝试所有交叉算子，选择产生最佳后代的算子
        crossover_methods = [
            CrossoverOperator.order_crossover,
            CrossoverOperator.partially_mapped_crossover,
            CrossoverOperator.edge_recombination_crossover
        ]
        
        best_offspring = None
        best_fitness = float('inf')
        
        for crossover_method in crossover_methods:
            try:
                # 应用交叉算子
                offspring1, offspring2 = crossover_method(parent1, parent2, solution_class)
                
                # 评估后代
                offspring1.evaluate(cost_params)
                offspring2.evaluate(cost_params)
                
                # 选择最好的后代
                current_best = offspring1 if offspring1.fitness < offspring2.fitness else offspring2
                
                # 更新全局最佳后代
                if current_best.fitness < best_fitness:
                    best_offspring = (offspring1, offspring2)
                    best_fitness = current_best.fitness
            except Exception as e:
                # 如果当前交叉方法失败，尝试下一个
                continue
        
        # 如果所有交叉方法都失败，返回父代的拷贝
        if best_offspring is None:
            return copy.deepcopy(parent1), copy.deepcopy(parent2)
            
        return best_offspring 