"""
调试充电计算的模块 - 高效统计版本
"""
import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def debug_charging(solution):
    """
    调试充电计算 - 高效统计版本

    参数:
        solution (Solution): 解决方案对象
    """
    print("\n===== 充电计算调试 =====")

    # 创建表格标题
    print(f"{'车辆ID':<6} {'充电站ID':<8} {'充电速率(kWh/min)':<18} {'排队时间(min)':<14} {'总充电时间(min)':<16} {'实际充电时间(min)':<18} {'充电量(kWh)':<12} {'充电效率(kWh/h)':<16}")
    print("-" * 120)

    # 总计数据
    total_charging_time = 0
    total_actual_charging_time = 0
    total_charged_energy = 0

    # 遍历所有车辆
    for v_idx, vehicle in enumerate(solution.vehicles):
        if vehicle.total_distance <= 0 or not hasattr(vehicle, 'total_charging_time') or vehicle.total_charging_time <= 0:
            continue  # 跳过未使用的车辆或未充电的车辆

        # 查找路径中的充电站
        charging_stations_used = []
        for node_id in vehicle.route:
            if node_id > len(solution.customers):
                station_idx = node_id - len(solution.customers) - 1
                if station_idx < len(solution.charging_stations):
                    charging_stations_used.append(solution.charging_stations[station_idx])

        if charging_stations_used:
            # 如果使用了多个充电站，按比例分配充电时间
            # 这里简化处理，假设每个充电站充电时间相等
            charging_time_per_station = vehicle.total_charging_time / len(charging_stations_used)

            # 计算每个充电站的充电量
            vehicle_charged_energy = 0
            vehicle_actual_charging_time = 0

            for station in charging_stations_used:
                # 从总充电时间中减去排队时间，得到实际充电时间
                actual_charging_time = max(0, charging_time_per_station - station.queue_time)
                charging_rate = station.charging_rate
                station_charge = actual_charging_time * charging_rate

                # 累加车辆数据
                vehicle_charged_energy += station_charge
                vehicle_actual_charging_time += actual_charging_time

                # 计算充电效率 (kWh/h)
                charging_efficiency = station_charge / (charging_time_per_station / 60) if charging_time_per_station > 0 else 0

                # 打印充电站数据
                print(f"{v_idx+1:<6} {station.id:<8} {station.charging_rate:<18.2f} {station.queue_time:<14.2f} {charging_time_per_station:<16.2f} {actual_charging_time:<18.2f} {station_charge:<12.2f} {charging_efficiency:<16.2f}")

            # 累加总计数据
            total_charging_time += vehicle.total_charging_time
            total_actual_charging_time += vehicle_actual_charging_time
            total_charged_energy += vehicle_charged_energy
        else:
            print(f"{v_idx+1:<6} {'未找到':<8} {'-':<18} {'-':<14} {vehicle.total_charging_time:<16.2f} {'-':<18} {'-':<12} {'-':<16}")

    # 打印总计
    print("-" * 120)
    avg_efficiency = total_charged_energy / (total_charging_time / 60) if total_charging_time > 0 else 0
    print(f"{'总计':<6} {'-':<8} {'-':<18} {'-':<14} {total_charging_time:<16.2f} {total_actual_charging_time:<18.2f} {total_charged_energy:<12.2f} {avg_efficiency:<16.2f}")

    # 打印总结
    print("\n充电计算总结:")
    print(f"1. 总充电时间: {total_charging_time:.2f} 分钟 ({total_charging_time/60:.4f} 小时)")
    print(f"2. 实际充电时间(不含排队): {total_actual_charging_time:.2f} 分钟 ({total_actual_charging_time/60:.4f} 小时)")
    print(f"3. 总充电量: {total_charged_energy:.2f} kWh")
    print(f"4. 平均充电效率: {avg_efficiency:.2f} kWh/h")
    # 避免除以零错误
    if total_charging_time > 0:
        queue_time_ratio = (total_charging_time - total_actual_charging_time) / total_charging_time * 100
        print(f"5. 排队时间占比: {queue_time_ratio:.2f}%")
    else:
        print("5. 排队时间占比: 0.00% (无充电操作)")

    # 验证充电量计算
    print("\n充电量计算验证:")
    print(f"- 根据实际充电时间和充电速率计算的充电量: {total_charged_energy:.2f} kWh")

    try:
        # 从solution对象获取充电成本
        charging_cost = getattr(solution, 'charging_cost', 0)

        # 使用固定的充电成本参数
        from src.config import COST_PARAMS
        cost_per_kwh = COST_PARAMS.get('charging_cost_per_kwh', 1.5862)

        # 计算报告中的充电量
        report_charged_energy = charging_cost / cost_per_kwh if cost_per_kwh > 0 else 0
        print(f"- 实验报告中显示的充电量: {report_charged_energy:.2f} kWh (充电成本: {charging_cost:.2f} 元, 单价: {cost_per_kwh:.4f} 元/kWh)")
    except Exception as e:
        print(f"- 无法计算实验报告中的充电量: {str(e)}")

    print("===== 调试结束 =====\n")

if __name__ == "__main__":
    print("此模块不适合直接运行，请从main.py调用")
