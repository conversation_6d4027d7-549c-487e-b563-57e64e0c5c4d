"""
电动车辆模型，实现车辆属性和部分充电策略逻辑
"""
import numpy as np
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.config import REFRIGERATION_CONFIG


class Vehicle:
    def __init__(self, id, max_load, battery_capacity, consumption_rate,
                 speed, partial_charging_factor=0.8, refrigeration_power=2.5,
                 refr_energy_ratio=0.7, temp_control_precision=0.9,
                 multi_temp_zones=False, idle_refr_power_ratio=0.4):
        """
        初始化电动车辆模型

        参数:
            id (int): 车辆ID
            max_load (float): 最大载重量(kg)
            battery_capacity (float): 电池容量(kWh)
            consumption_rate (float): 单位距离耗电量(kWh/km)
            speed (float): 行驶速度(km/h)
            partial_charging_factor (float): 部分充电因子(0-1之间)，默认为0.8
            refrigeration_power (float): 制冷设备功率(kW)
            refr_energy_ratio (float): 制冷系统能效比
            temp_control_precision (float): 温度控制精度(0-1之间)
            multi_temp_zones (bool): 是否支持多温区
            idle_refr_power_ratio (float): 停车时制冷功率比例
        """
        self.id = id
        self.max_load = max_load
        self.battery_capacity = battery_capacity
        self.consumption_rate = consumption_rate
        self.speed = speed
        self.partial_charging_factor = partial_charging_factor

        # 制冷系统参数
        self.refrigeration_power = refrigeration_power
        self.refr_energy_ratio = refr_energy_ratio
        self.temp_control_precision = temp_control_precision
        self.multi_temp_zones = multi_temp_zones
        self.idle_refr_power_ratio = idle_refr_power_ratio

        # 动态状态
        self.current_load = 0
        self.current_battery = battery_capacity  # 初始满电
        self.current_position = None
        self.route = []
        self.schedule = []  # 包含到达时间、服务时间、离开时间等
        self.total_distance = 0
        self.total_travel_time = 0
        self.total_waiting_time = 0
        self.total_charging_time = 0

        # 制冷相关状态
        self.total_refrigeration_time = 0
        self.refrigeration_energy_consumption = 0
        self.temperature_violations = []
        self.current_temp = REFRIGERATION_CONFIG['default_temp']  # 使用配置文件中的默认温度
        # 创建温区字典
        self.temp_zone_customers = {zone['name']: [] for zone in REFRIGERATION_CONFIG['temp_zones']}
        self.refr_on = False  # 制冷系统是否开启
        self.refr_state_changes = 0  # 制冷系统启停次数

    def reset(self):
        """重置车辆状态"""
        self.current_load = 0
        self.current_battery = self.battery_capacity
        self.current_position = None
        self.route = []
        self.schedule = []
        self.total_distance = 0
        self.total_travel_time = 0
        self.total_waiting_time = 0
        self.total_charging_time = 0

        # 重置制冷相关状态
        self.total_refrigeration_time = 0
        self.refrigeration_energy_consumption = 0
        self.temperature_violations = []
        self.current_temp = REFRIGERATION_CONFIG['default_temp']
        self.temp_zone_customers = {zone['name']: [] for zone in REFRIGERATION_CONFIG['temp_zones']}
        self.refr_on = False
        self.refr_state_changes = 0

    def can_serve(self, customer, distance_matrix):
        """
        检查车辆是否能够服务指定客户

        参数:
            customer: 客户对象
            distance_matrix: 距离矩阵

        返回:
            bool: 是否能够服务
        """
        # 检查载重约束
        if self.current_load + customer.demand > self.max_load:
            return False

        # 如果当前没有位置，表示车辆在配送中心
        if self.current_position is None:
            distance = distance_matrix[0][customer.id]
        else:
            distance = distance_matrix[self.current_position][customer.id]

        # 计算行驶时间
        travel_time = distance / self.speed * 60  # 转换为分钟

        # 计算制冷能耗
        refr_energy = self.calculate_refrigeration_energy(travel_time)

        # 检查电量约束 - 考虑往返距离和制冷能耗
        energy_to_customer = distance * self.consumption_rate + refr_energy
        distance_to_depot = distance_matrix[customer.id][0]
        # 考虑回程制冷能耗
        return_time = distance_to_depot / self.speed * 60
        return_refr_energy = self.calculate_refrigeration_energy(return_time)
        energy_to_depot = distance_to_depot * self.consumption_rate + return_refr_energy

        return self.current_battery >= energy_to_customer + energy_to_depot

    def calculate_refrigeration_energy(self, time_minutes, is_idle=False):
        """
        计算制冷系统在指定时间内的能耗

        参数:
            time_minutes (float): 时间(分钟)
            is_idle (bool): 是否在停车状态

        返回:
            float: 制冷能耗(kWh)
        """
        # 如果制冷系统未开启，返回0
        if not self.refr_on:
            return 0

        # 根据运行状态确定功率
        if is_idle:
            power = self.refrigeration_power * self.idle_refr_power_ratio
        else:
            power = self.refrigeration_power

        # 如果支持多温区且有不同温区的客户，增加能耗
        if self.multi_temp_zones and len([zone for zone, customers in self.temp_zone_customers.items() if customers]) > 1:
            power *= 1.2  # 多温区额外能耗

        # 计算能耗 (kW * h)
        energy = power * (time_minutes / 60) / self.refr_energy_ratio

        return energy

    def update_temperature(self, time_minutes, ambient_temp=None, is_idle=False):
        """
        更新箱内温度

        参数:
            time_minutes (float): 经过的时间(分钟)
            ambient_temp (float): 环境温度(°C)，如果不提供则使用配置文件中的值
            is_idle (bool): 是否在停车状态

        返回:
            float: 更新后的箱内温度(°C)
        """
        # 使用配置文件中的环境温度（如果未提供）
        if ambient_temp is None:
            ambient_temp = REFRIGERATION_CONFIG['ambient_temp']

        # 确定目标温度 - 根据车辆上不同温区客户的要求
        target_temp = self._determine_target_temperature()

        # 计算温度变化
        if self.refr_on:
            # 制冷系统开启时，温度向目标温度靠近
            cooling_efficiency = self.temp_control_precision
            if is_idle:
                cooling_efficiency *= 0.8  # 停车时制冷效率降低

            # 温度变化率 (°C/min)
            change_rate = -0.3 * cooling_efficiency

            # 计算新温度
            temp_diff = self.current_temp - target_temp
            if temp_diff > 0:  # 当前温度高于目标温度，需要降温
                delta_temp = max(change_rate * time_minutes, -temp_diff)
            else:  # 当前温度已经达到或低于目标温度
                delta_temp = 0
        else:
            # 制冷系统关闭时，温度向环境温度靠近
            # 温度上升率随温差增大而增大
            temp_diff = ambient_temp - self.current_temp
            # 使用配置文件中的温度上升率
            rise_rate = REFRIGERATION_CONFIG['temp_rise_rate']
            if is_idle:
                rise_rate *= 1.5  # 停车时温度上升更快

            delta_temp = min(rise_rate * time_minutes, temp_diff)

        # 更新温度
        self.current_temp += delta_temp

        return self.current_temp

    def _determine_target_temperature(self):
        """
        确定制冷目标温度

        返回:
            float: 目标温度(°C)
        """
        # 查找所有需要服务的客户的温度需求
        temp_requirements = []

        # 检查每个温区是否有客户需要服务
        for zone in REFRIGERATION_CONFIG['temp_zones']:
            zone_name = zone['name']
            if self.temp_zone_customers[zone_name]:
                temp_requirements.append(zone['temp_range'][0])  # 使用温区最低温度

        # 如果没有特定温度需求，返回默认值
        if not temp_requirements:
            return REFRIGERATION_CONFIG['default_temp']

        # 如果支持多温区，返回最低需求温度
        if self.multi_temp_zones:
            return min(temp_requirements)
        else:
            # 不支持多温区，返回最主要客户类型的温度
            max_count = 0
            main_temp = REFRIGERATION_CONFIG['default_temp']

            for zone in REFRIGERATION_CONFIG['temp_zones']:
                zone_name = zone['name']
                count = len(self.temp_zone_customers[zone_name])
                if count > max_count:
                    max_count = count
                    main_temp = zone['temp_range'][0]

            return main_temp

    def toggle_refrigeration(self, state=None):
        """
        切换制冷系统状态

        参数:
            state (bool, optional): 指定状态，如果不提供则切换当前状态

        返回:
            bool: 切换后的状态
        """
        prev_state = self.refr_on

        if state is None:
            self.refr_on = not self.refr_on
        else:
            self.refr_on = state

        # 如果状态发生改变，记录启停次数
        if prev_state != self.refr_on:
            self.refr_state_changes += 1

        return self.refr_on

    def add_customer_to_temp_zone(self, customer):
        """
        将客户添加到对应温区

        参数:
            customer: 客户对象
        """
        zone = customer.temp_requirement['zone']
        if zone in self.temp_zone_customers:
            if customer not in self.temp_zone_customers[zone]:
                self.temp_zone_customers[zone].append(customer)

    def calculate_partial_charging_time(self, required_energy, charging_rate):
        """
        计算部分充电所需时间

        参数:
            required_energy (float): 需要充电的电量(kWh)
            charging_rate (float): 充电速率(kWh/min)

        返回:
            float: 充电时间(min)
        """
        # 根据部分充电策略，只充电到一定比例
        target_level = self.battery_capacity * self.partial_charging_factor

        # 如果当前电量已经超过目标充电水平，则不需要充电
        if self.current_battery >= target_level:
            return 0

        # 计算需要充电的量
        energy_to_charge = min(required_energy, target_level - self.current_battery)

        # 计算充电时间
        charging_time = energy_to_charge / charging_rate

        return charging_time

    def update_after_charging(self, charging_time, charging_rate, queue_time=None):
        """
        更新充电后的状态

        参数:
            charging_time (float): 充电时间(min)，包括排队时间和实际充电时间
            charging_rate (float): 充电速率(kWh/min)
            queue_time (float, optional): 排队时间(min)，如果提供则使用，否则假设为0
        """
        # 如果充电时间为0，则不进行任何操作
        if charging_time <= 0:
            return

        # 计算充电量（不考虑排队时间）
        # 注意：传入的充电时间已经包含了排队时间，但充电量计算时不应包括排队时间
        # 因此，我们需要从总时间中减去排队时间

        # 如果提供了排队时间，则使用它；否则假设排队时间为0
        queue_time = queue_time or 0

        # 计算实际充电时间（总充电时间减去排队时间）
        actual_charging_time = max(0, charging_time - queue_time)

        # 计算充电量
        energy_charged = actual_charging_time * charging_rate

        # 更新电池电量
        self.current_battery += energy_charged
        self.total_charging_time += charging_time

        # 确保电量不超过电池容量或部分充电目标
        target_level = self.battery_capacity * self.partial_charging_factor
        self.current_battery = min(self.current_battery, target_level)

        # 计算充电期间的制冷能耗
        refr_energy = self.calculate_refrigeration_energy(charging_time, is_idle=True)
        self.refrigeration_energy_consumption += refr_energy
        self.current_battery -= refr_energy

        # 更新箱内温度
        self.update_temperature(charging_time, is_idle=True)

    def update_after_travel(self, next_position, distance, customer=None):
        """
        更新行驶后的状态

        参数:
            next_position (int): 下一个位置的ID
            distance (float): 行驶距离(km)
            customer (Customer, optional): 如果下一个位置是客户，则提供客户对象

        返回:
            float: 行驶时间(min)
        """
        # 更新位置
        self.current_position = next_position

        # 更新路径
        self.route.append(next_position)

        # 更新行驶距离
        self.total_distance += distance

        # 更新行驶时间
        travel_time = distance / self.speed * 60  # 转换为分钟
        self.total_travel_time += travel_time

        # 当前到达时间（相对于行程开始）
        current_arrival_time = self.total_travel_time

        # 如果是客户点，处理时间窗口约束和等待时间
        if customer:
            # 检查是否需要等待（提前到达）
            if current_arrival_time < customer.earliest_time:
                # 计算等待时间
                waiting_time = customer.earliest_time - current_arrival_time

                # 更新总等待时间
                self.total_waiting_time += waiting_time

                # 记录调度信息
                self.schedule.append({
                    'node_id': next_position,
                    'arrival_time': current_arrival_time,
                    'waiting_time': waiting_time,
                    'service_time': customer.service_time,
                    'departure_time': customer.earliest_time + customer.service_time
                })

                # 计算等待期间的制冷能耗
                waiting_refr_energy = self.calculate_refrigeration_energy(waiting_time, is_idle=True)
                self.refrigeration_energy_consumption += waiting_refr_energy
                self.current_battery -= waiting_refr_energy

                # 更新箱内温度（等待期间）
                self.update_temperature(waiting_time, is_idle=True)
            else:
                # 无需等待
                self.schedule.append({
                    'node_id': next_position,
                    'arrival_time': current_arrival_time,
                    'waiting_time': 0,
                    'service_time': customer.service_time,
                    'departure_time': current_arrival_time + customer.service_time
                })

            # 更新载重
            self.current_load += customer.demand

            # 添加到对应温区
            self.add_customer_to_temp_zone(customer)

            # 检查温度违规
            violation = customer.calculate_temp_violation(self.current_temp)
            if violation > 0:
                self.temperature_violations.append((customer.id, violation, self.current_temp))
        else:
            # 非客户点（充电站或配送中心）
            self.schedule.append({
                'node_id': next_position,
                'arrival_time': current_arrival_time,
                'waiting_time': 0,
                'service_time': 0,
                'departure_time': current_arrival_time
            })

        # 计算并更新制冷能耗（行驶期间）
        refr_energy = self.calculate_refrigeration_energy(travel_time, is_idle=False)
        self.refrigeration_energy_consumption += refr_energy

        # 更新电量 - 包括行驶能耗和制冷能耗
        energy_consumed = distance * self.consumption_rate + refr_energy
        self.current_battery -= energy_consumed

        # 更新箱内温度（行驶期间）
        self.update_temperature(travel_time, is_idle=False)

        return travel_time

    def __str__(self):
        """字符串表示"""
        return f"Vehicle {self.id}: Load={self.current_load}/{self.max_load}, Battery={self.current_battery:.2f}/{self.battery_capacity}, Temp={self.current_temp:.1f}°C, RefOn={self.refr_on}"