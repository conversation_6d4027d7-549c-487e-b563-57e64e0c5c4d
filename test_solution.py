"""
测试 Solution 类是否正确删除了 waiting_cost 属性
"""
import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.models.solution import Solution
from src.models.vehicle import Vehicle
from src.models.customer import Customer
from src.models.charging import ChargingStation

# 创建测试数据
vehicles = [Vehicle(id=1, max_load=100, battery_capacity=40)]
customers = [Customer(id=1, x=0, y=0, demand=10, time_window=(0, 100))]
charging_stations = [ChargingStation(id=1, x=10, y=10)]
distance_matrix = [[0, 10, 20], [10, 0, 15], [20, 15, 0]]

# 创建一个 Solution 对象
solution = Solution(vehicles, customers, charging_stations, distance_matrix)

# 检查 solution 对象是否有 waiting_cost 属性
if hasattr(solution, 'waiting_cost'):
    print("错误: Solution 对象仍然有 waiting_cost 属性")
else:
    print("正确: Solution 对象没有 waiting_cost 属性")

# 检查 solution 对象是否有 early_arrival_penalty 和 late_arrival_penalty 属性
if hasattr(solution, 'early_arrival_penalty') and hasattr(solution, 'late_arrival_penalty'):
    print("正确: Solution 对象有 early_arrival_penalty 和 late_arrival_penalty 属性")
else:
    print("错误: Solution 对象没有 early_arrival_penalty 或 late_arrival_penalty 属性")
