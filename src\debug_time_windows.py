"""
调试时间窗口计算的模块
"""
import os
import sys

# 导入成本参数
from src.config import COST_PARAMS

def debug_time_windows(solution):
    """
    调试时间窗口计算

    参数:
        solution (Solution): 解决方案对象
    """
    print("\n===== 调试时间窗口计算 =====")

    # 总等待时间和提前到达时间
    total_waiting_time = 0
    total_early_time = 0
    total_late_time = 0

    # 遍历所有车辆
    for v_idx, vehicle in enumerate(solution.vehicles):
        if vehicle.total_distance <= 0:
            continue  # 跳过未使用的车辆

        print(f"\n车辆 {v_idx+1}:")
        print(f"  路径: {vehicle.route}")
        print(f"  总等待时间: {vehicle.total_waiting_time} 分钟 ({vehicle.total_waiting_time/60:.4f} 小时)")

        # 累积总等待时间
        total_waiting_time += vehicle.total_waiting_time

        # 检查 route 和 schedule 的长度是否一致
        if len(vehicle.route) != len(vehicle.schedule):
            print(f"  警告: route 长度 ({len(vehicle.route)}) 与 schedule 长度 ({len(vehicle.schedule)}) 不一致!")

        # 打印 schedule 详情
        print("  调度详情:")
        for i, node in enumerate(vehicle.schedule):
            node_id = node['node_id']
            arrival_time = node['arrival_time']
            waiting_time = node['waiting_time']
            service_time = node['service_time']
            departure_time = node['departure_time']

            # 获取对应的路径节点ID（如果索引有效）
            route_node_id = vehicle.route[i] if i < len(vehicle.route) else "无效索引"

            # 检查节点ID是否一致
            id_match = "匹配" if node_id == route_node_id else "不匹配"

            print(f"    节点 {i+1}: ID={node_id} (route ID={route_node_id}) {id_match}, 到达={arrival_time}, 等待={waiting_time}, 服务={service_time}, 离开={departure_time}")

            # 如果是客户节点，检查时间窗口
            if node_id > 0 and node_id <= len(solution.customers):
                customer = solution.customers[node_id - 1]
                time_window = customer.time_window
                earliest_time = customer.earliest_time
                latest_time = customer.latest_time

                print(f"      客户时间窗口: [{earliest_time}, {latest_time}]")

                # 检查提前到达
                if arrival_time < earliest_time:
                    early_time = earliest_time - arrival_time
                    early_hours = early_time / 60.0
                    print(f"      提前到达: {early_time} 分钟 ({early_hours:.4f} 小时)")
                    total_early_time += early_time

                    # 检查等待时间是否与提前到达时间一致
                    if waiting_time != early_time:
                        print(f"      警告: 等待时间 ({waiting_time}) 与提前到达时间 ({early_time}) 不一致!")

                # 检查延迟到达
                if arrival_time > latest_time:
                    late_time = arrival_time - latest_time
                    late_hours = late_time / 60.0
                    print(f"      延迟到达: {late_time} 分钟 ({late_hours:.4f} 小时)")
                    total_late_time += late_time

    # 打印总结
    print("\n总结:")
    print(f"  总等待时间: {total_waiting_time} 分钟 ({total_waiting_time/60:.4f} 小时)")
    print(f"  总提前到达时间: {total_early_time} 分钟 ({total_early_time/60:.4f} 小时)")
    print(f"  总延迟到达时间: {total_late_time} 分钟 ({total_late_time/60:.4f} 小时)")

    # 计算成本 - 使用配置文件中的参数
    time_penalty_early = COST_PARAMS['time_penalty_early']
    time_penalty_late = COST_PARAMS['time_penalty_late']

    print(f"  成本参数: 提前到达惩罚={time_penalty_early}元/小时, 延迟到达惩罚={time_penalty_late}元/小时")

    early_penalty = (total_early_time / 60) * time_penalty_early
    late_penalty = (total_late_time / 60) * time_penalty_late

    print(f"  提前到达惩罚: {early_penalty:.2f} 元 (每小时 {time_penalty_early} 元)")
    print(f"  延迟到达惩罚: {late_penalty:.2f} 元 (每小时 {time_penalty_late} 元)")

    # 检查与解决方案中的值是否一致
    solution_early_penalty = getattr(solution, 'early_arrival_penalty', 0)
    solution_late_penalty = getattr(solution, 'late_arrival_penalty', 0)

    print(f"\n解决方案中的值:")
    print(f"  提前到达惩罚: {solution_early_penalty:.2f} 元")
    print(f"  延迟到达惩罚: {solution_late_penalty:.2f} 元")

    # 检查差异
    early_diff = abs(early_penalty - solution_early_penalty)
    late_diff = abs(late_penalty - solution_late_penalty)

    if early_diff > 0.01:
        print(f"  警告: 计算的提前到达惩罚 ({early_penalty:.2f}) 与解决方案中的值 ({solution_early_penalty:.2f}) 相差 {early_diff:.2f} 元!")

    if late_diff > 0.01:
        print(f"  警告: 计算的延迟到达惩罚 ({late_penalty:.2f}) 与解决方案中的值 ({solution_late_penalty:.2f}) 相差 {late_diff:.2f} 元!")

    print("===== 调试结束 =====\n")

if __name__ == "__main__":
    print("此模块不适合直接运行，请从main.py调用")
