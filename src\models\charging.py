"""
充电站模型，包含充电站位置和充电特性
"""

class ChargingStation:
    def __init__(self, id, x, y, charging_rate, queue_time=0):
        """
        初始化充电站模型
        
        参数:
            id (int): 充电站ID
            x (float): x坐标
            y (float): y坐标
            charging_rate (float): 充电速率(kWh/min)
            queue_time (float): 平均排队时间(min)
        """
        self.id = id
        self.x = x
        self.y = y
        self.charging_rate = charging_rate
        self.queue_time = queue_time
        
    def calculate_total_charging_time(self, energy_required, queue_time_factor=1.0):
        """
        计算总充电时间，包括排队时间
        
        参数:
            energy_required (float): 需要充电的电量(kWh)
            queue_time_factor (float): 排队时间因子，可用于模拟不同时段的拥堵情况
            
        返回:
            float: 总充电时间(min)
        """
        charging_time = energy_required / self.charging_rate
        total_time = charging_time + (self.queue_time * queue_time_factor)
        return total_time
    
    def __str__(self):
        """字符串表示"""
        return f"ChargingStation {self.id}: Pos=({self.x}, {self.y}), Rate={self.charging_rate}kWh/min" 